# Auth Store Implementation

This document describes the authentication store implementation for the Epic Padel website.

## Overview

The auth store is implemented using Zustand with persistence to manage user authentication state across the application. It provides a centralized way to handle login, logout, and user data management.

## Files Created/Modified

### New Files
- `src/store/auth-store.ts` - Main auth store with Zustand
- `src/components/layouts/protected-layout.tsx` - Protected route wrapper
- `src/components/layouts/profile-layout.tsx` - Profile page layout with sidebar
- `src/components/providers/auth-provider.tsx` - Auth initialization provider
- `src/app/profile/subscription/page.tsx` - Subscription management page
- `src/app/profile/settings/page.tsx` - User settings page
- `src/utils/auth.ts` - Auth utility functions

### Modified Files
- `src/app/layout.tsx` - Added AuthProvider wrapper
- `src/app/profile/page.tsx` - Updated to use ProfileLayout
- `src/components/auth/login-button.tsx` - Updated to use auth store
- `src/api/auth-service.ts` - Updated to use auth store

## Auth Store Features

### State Management
- `user`: Current user data (User | null)
- `token`: Authentication token (string | null)
- `isAuthenticated`: Authentication status (boolean)
- `isLoading`: Loading state (boolean)

### Actions
- `login(authData)`: Handle user login
- `logout()`: Handle user logout
- `updateUser(userData)`: Update user data
- `setLoading(loading)`: Set loading state
- `checkAuth()`: Validate token with server
- `initializeAuth()`: Initialize auth from localStorage

### Data Persistence
- Uses Zustand persist middleware
- Syncs with localStorage for backward compatibility
- Automatically validates tokens on app start

## User Data Types

```typescript
interface User {
  id: number;
  dob?: string;
  profile_image?: ProfileImage;
  user_email?: UserEmail;
  user_phone?: UserPhone;
  userCurrentPackage?: UserCurrentPackage;
  active_purchase_subscription?: ActivePurchaseSubscription;
}
```

## Protected Routes

### ProtectedLayout Component
- Checks authentication status
- Shows loading state while validating
- Redirects unauthenticated users to home with login dialog
- Can be configured with `requireAuth` prop

### ProfileLayout Component
- Extends ProtectedLayout
- Provides sidebar navigation for profile sections
- Includes user profile header
- Handles logout confirmation

## Usage Examples

### Using Auth Store in Components
```typescript
import { useAuthStore } from "@/store/auth-store";

const MyComponent = () => {
  const { user, isAuthenticated, logout } = useAuthStore();
  
  if (!isAuthenticated) {
    return <div>Please login</div>;
  }
  
  return <div>Welcome {user?.user_email?.email}</div>;
};
```

### Creating Protected Pages
```typescript
import ProtectedLayout from "@/components/layouts/protected-layout";

const ProtectedPage = () => {
  return (
    <ProtectedLayout>
      <div>This content is only visible to authenticated users</div>
    </ProtectedLayout>
  );
};
```

### Using Profile Layout
```typescript
import ProfileLayout from "@/components/layouts/profile-layout";

const ProfilePage = () => {
  return (
    <ProfileLayout activeSection="profile">
      <div>Profile content here</div>
    </ProfileLayout>
  );
};
```

## Authentication Flow

1. **App Initialization**: AuthProvider initializes auth state from localStorage
2. **Token Validation**: Automatically validates token with server on app start
3. **Login**: Auth service functions update store on successful login
4. **Route Protection**: ProtectedLayout checks auth status before rendering
5. **Logout**: Clears all auth data and redirects to home

## Profile Pages

The implementation includes several profile pages:

- `/profile` - Main profile page with user information
- `/profile/subscription` - Subscription management
- `/profile/settings` - User settings and preferences

Each page uses the ProfileLayout which provides:
- Consistent sidebar navigation
- User profile header
- Logout functionality
- Active section highlighting

## Integration with Existing Code

The auth store maintains backward compatibility with existing localStorage-based authentication:
- Still stores data in localStorage
- Works with existing auth service functions
- Maintains same data structure

## Security Considerations

- Tokens are validated with server on app start
- Invalid tokens are automatically cleared
- Protected routes redirect unauthenticated users
- Logout clears all stored authentication data

## Future Enhancements

Potential improvements:
- Token refresh mechanism
- Role-based access control
- Session timeout handling
- Multi-device session management
- Enhanced error handling for network failures
