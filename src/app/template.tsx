"use client";

import gsap from "gsap";
import Image from "next/image";
import { ReactNode, useEffect } from "react";

export default function Template({ children }: { children: ReactNode }) {
  useEffect(() => {
    // Create the animation timeline
    const tl = gsap.timeline();

    // Animate the bars
    tl.to(".bar", {
      duration: 0.5,
      height: 0,
      stagger: {
        amount: 0.3,
      },
      ease: "power4.inOut",
    });

    // Fade out the loader
    setTimeout(() => {
      gsap.to(".page-loader", {
        duration: 1,
        opacity: 0,
        onComplete: () => {
          const loader = document.querySelector(".page-loader") as HTMLElement;
          if (loader) {
            loader.style.display = "none";
          }
        },
      });
    }, 1000);
  }, []);

  return (
    <>
      <div className="page-loader">
        <div className="page-loader-logo hide-animation">
          <Image
            alt="epic logo"
            title="epic logo"
            src="/imgs/template/logo.svg"
            width={200}
            height={100}
            className="h-auto w-32 md:w-48"
            loading="lazy"
          />
        </div>
        <div className="bar"></div>
        <div className="bar"></div>
        <div className="bar"></div>
        <div className="bar"></div>
        <div className="bar"></div>
        <div className="bar"></div>
        <div className="bar"></div>
        <div className="bar"></div>
        <div className="bar"></div>
        <div className="bar"></div>
      </div>
      {children}
    </>
  );
}
