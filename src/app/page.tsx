import dynamic from "next/dynamic";

const SectionSkeleton = ({ height = "50vh" }: { height?: string }) => (
  <div className="w-full animate-pulse bg-gray-100 dark:bg-gray-800" style={{ height }} />
);

const ErrorFallback = ({ error }: { error: Error }) => (
  <div className="w-full p-4 text-center text-red-500">
    <p>Something went wrong loading this section.</p>
    <p className="text-sm">{error.message}</p>
  </div>
);

// Dynamic imports with proper configuration
const HeaderSection = dynamic(
  () => import("@/components/home/<USER>").then((mod) => mod.default),
  {
    loading: () => <SectionSkeleton />,
    ssr: true,
  }
);

const LocationSection = dynamic(
  () => import("@/components/home/<USER>").then((mod) => mod.default),
  {
    loading: () => <SectionSkeleton />,
  }
);

const MembershipSection = dynamic(
  () => import("@/components/home/<USER>").then((mod) => mod.default),
  {
    loading: () => <SectionSkeleton />,
  }
);

const TestimonialSection = dynamic(
  () => import("@/components/home/<USER>").then((mod) => mod.default),
  {
    loading: () => <SectionSkeleton />,
  }
);

const BlogsSection = dynamic(
  () => import("@/components/home/<USER>").then((mod) => mod.default),
  {
    loading: () => <SectionSkeleton />,
  }
);

const DownloadAppSection = dynamic(
  () => import("@/components/home/<USER>").then((mod) => mod.default),
  {
    loading: () => <SectionSkeleton />,
  }
);

const FaqSection = dynamic(
  () => import("@/components/home/<USER>").then((mod) => mod.default),
  {
    loading: () => <SectionSkeleton />,
  }
);

const ThankYouDialog = dynamic(
  () => import("@/components/dialogs/thank-you-dialog").then((mod) => mod.default),
  { loading: () => <SectionSkeleton /> }
);

export default function Home() {
  return (
    <div className="flex min-h-screen w-full flex-col items-center justify-center">
      <HeaderSection />
      <LocationSection />
      <MembershipSection />
      <TestimonialSection />
      <BlogsSection />
      <DownloadAppSection />
      <FaqSection />
      <ThankYouDialog />
    </div>
  );
}
