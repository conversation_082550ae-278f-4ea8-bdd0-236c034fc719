"use client";

import React, { useEffect, useRef } from "react";
import Image from "next/image";
import gsap from "gsap";

const TeamPage = () => {
  const sliderRef = useRef<HTMLDivElement>(null);
  const currentImageIndex = useRef(0);
  const images = [
    {
      src: "/imgs/page/team/team1.jpg",
      alt: "<PERSON>",
    },
    {
      src: "/imgs/page/team/team3.jpg",
      alt: "<PERSON> - COO",
    },
  ];

  useEffect(() => {
    if (!sliderRef.current) return;

    const slider = sliderRef.current;
    const slides = slider.querySelectorAll(".slide");

    // Initial setup
    gsap.set(slides[1], { x: "100%" });

    // Animation function
    const animateSlides = () => {
      const currentSlide = slides[currentImageIndex.current];
      const nextSlide = slides[(currentImageIndex.current + 1) % slides.length];

      // Animate current slide out
      gsap.to(currentSlide, {
        x: "-100%",
        duration: 1,
        ease: "power2.inOut",
      });

      // Animate next slide in
      gsap.to(nextSlide, {
        x: "-100%",
        duration: 1,
        ease: "power2.inOut",
      });

      // Update current index
      currentImageIndex.current = (currentImageIndex.current + 1) % slides.length;
    };

    // Set up interval for automatic slideshow
    const interval = setInterval(animateSlides, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <main className="bg-background min-h-screen">
      <section className="relative py-20 md:py-32">
        <div className="container mx-auto px-4">
          <div className="relative">
            <div className="text-center">
              <h2 className="text-primary mb-6 text-6xl font-bold">Meet Our Team</h2>
            </div>
          </div>
          <div className="mt-12">
            <div className="flex justify-center">
              <div className="w-full max-w-2xl">
                <div className="overflow-hidden rounded-[20px]">
                  <div className="relative h-[400px]" ref={sliderRef}>
                    {images.map((image, index) => (
                      <div
                        key={image.src}
                        className="slide absolute top-0 left-0 h-full w-full"
                        style={{
                          left: index === 0 ? "0%" : "100%",
                          zIndex: index === 0 ? 1 : 0,
                        }}
                      >
                        <div className="relative h-full w-full">
                          <Image
                            src={image.src}
                            alt={image.alt}
                            fill
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            className="object-cover object-top"
                            priority={index === 0}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="mt-6 text-center">
                  <h3 className="text-primary text-2xl font-bold">Tim Bainton</h3>
                  <h5 className="mt-2 text-lg text-gray-400">COO</h5>
                  <p className="mx-5 mt-4 text-justify text-gray-400">
                    In addition to being the COO of Epic Padel, Bainton is also the President of
                    Blue Chip Sports Management and is considered one of the leading authorities on
                    sports facility operations and management in the world. He has published three
                    books and over one hundred published articles covering the business aspects of
                    the sports industry. He is a professor at Marymount University in the college of
                    BILT. He has been a keynote speaker across the globe and has been featured in
                    major news outlets for his work in the Sports Industry. He has been featured on
                    the cover of Club Business International and had a featured column in Club
                    Solutions Magazine. He currently serves on the Advisory Board for St Jude's
                    Children's Hospital. Bainton holds an undergraduate degree in Economics from
                    George Mason and has graduate degrees in Business and Sports Industry Management
                    from Georgetown. He is also a doctoral candidate in Business Intelligence
                    through the Marymount COLLEGE of BILT. Bainton was a heavily recruited,
                    nationally ranked junior in his native England, going on to play on a full
                    scholarship at Division I George Mason University. He resides in ALEXANDRIA,
                    VIRGINIA, with Chrissy and their daughter Hattie.
                  </p>
                  <a
                    href="https://www.linkedin.com/in/timbainton/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="mt-6 inline-block"
                  >
                    <Image
                      src="/imgs/template/icons/linkedin-green.png"
                      alt="linkedin"
                      title="linkedin"
                      width={40}
                      height={40}
                      className="transition-transform duration-300 hover:scale-110"
                    />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
};

export default TeamPage;
