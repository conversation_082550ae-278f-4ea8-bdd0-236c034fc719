import Link from "next/link";
import Image from "next/image";

export default function NotFound() {
  return (
    <main className="flex min-h-[calc(100vh-200px)] flex-col items-center justify-center px-4 py-16 text-center">
      <div className="flex items-center gap-3">
        <Image
          src="/imgs/template/logo-green-small.svg"
          alt="Epic Padel Logo"
          width={65}
          height={65}
          className="mb-6"
          loading="lazy"
        />
        <h1 className="text-primary font-helvetica mb-4 text-6xl font-bold">404</h1>
      </div>
      <h2 className="mb-6 text-2xl font-semibold">Page Not Found</h2>
      <p className="mb-8 max-w-md text-gray-600">
        Oops! The page you're looking for seems to have disappeared into the void. Let's get you
        back on track.
      </p>
      <Link
        href="/"
        className="bg-primary hover:bg-primary/90 font-helvetica fond-bold rounded-full px-6 py-3 text-white transition-colors"
      >
        Return Home
      </Link>
    </main>
  );
}
