import FacebookPixel from "@/components/facebook-pixel";
import Footer from "@/components/footer";
import GTMBody from "@/components/gtm/gtm-body";
import GTMHeader from "@/components/gtm/gtm-header";
import HomeJsonLd from "@/components/json-lds/home-json-ld";
import NavBar from "@/components/nav-bar";
import AuthProvider from "@/components/providers/auth-provider";
import ClarityProvider from "@/components/providers/clarity-provider";
import { helvetica, neueHaasUnica } from "@/fonts";
import type { Metadata, Viewport } from "next";
import React from "react";
import { Toaster } from "sonner";
import "./globals.css";

export const viewport: Viewport = {
  themeColor: "#1b5632",
};

export const metadata: Metadata = {
  title: "Epic Padel",
  description:
    "EPIC is a premier padel sports management and investment company based in Arlington, VA, focused on becoming the leading operator of padel clubs across North America.",
  keywords:
    "padel, padel sports, padel clubs, padel management, sports investment, EPIC padel, padel North America, padel USA, padel Arlington VA, padel growth, padel operator, padel company, sports management, padel business",
  authors: [{ name: "EPIC Padel" }],
  openGraph: {
    title: "Epic Padel",
    description:
      "EPIC is a premier padel sports management and investment company based in Arlington, VA, focused on becoming the leading operator of padel clubs across North America.",
    url: `${process.env.NEXT_PUBLIC_APP_URL!}`,
    siteName: "Epic Padel",
    images: [`https://epic-padel.com/imgs/opengraph-image.jpg`],
    locale: "en_US",
    type: "website",
    videos: [
      {
        url: `${process.env.NEXT_PUBLIC_APP_URL!}/videos/epic_padel_h.mp4`,
        width: 1920,
        height: 1080,
      },
      {
        url: `${process.env.NEXT_PUBLIC_APP_URL!}/videos/epic_padel_v.mp4`,
        width: 720,
        height: 1280,
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Epic Padel",
    description:
      "Join Epic Padel and experience premium padel clubs designed for community, performance, and growth.",
    images: [`https://epic-padel.com/imgs/twitter-image.jpg`],
  },
  other: {
    "msapplication-TileColor": "#1b5632",
    "template-color": "#1b5632",
  },
  alternates: {
    canonical: "https://epic-padel.com",
  },
  appLinks: {
    ios: {
      url: "https://apps.apple.com/ae/app/epic-padel/id6501984934",
      app_name: "epic-padel",
      app_store_id: "6501984934",
    },
    android: {
      url: "https://play.google.com/store/apps/details?id=com.epicpadel.mobile",
      package: "com.epicpadel.mobile",
      app_name: "epic-padel",
    },
    web: {
      url: "https://epic-padel.com",
      should_fallback: true,
    },
  },
  category: "Sports",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <HomeJsonLd />
        <GTMHeader />
      </head>
      <body
        className={`${neueHaasUnica.variable} ${helvetica.variable} page-transition antialiased`}
      >
        <ClarityProvider />
        <FacebookPixel />
        <GTMBody />
        <AuthProvider>
          <Toaster richColors position="top-right" />
          <NavBar />
          {children}
          <Footer />
        </AuthProvider>
      </body>
    </html>
  );
}
