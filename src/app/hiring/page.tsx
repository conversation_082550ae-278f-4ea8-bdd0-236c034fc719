"use client";

import { useAuthStore } from "@/store/auth-store";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { sendHiringEmail } from "../actions/hiring";
import { toast } from "sonner";
import { z } from "zod";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

const hiringFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  "value-add": z.string().min(10, "Please provide a detailed response about how you can add value"),
  "pdf-file": z.any(),
});

type HiringFormData = z.infer<typeof hiringFormSchema>;

const HiringPage = () => {
  const user = useAuthStore((state) => state.user);
  const sectionRef = useRef<HTMLDivElement>(null);
  const headingRef = useRef<HTMLHeadingElement>(null);
  const formContainerRef = useRef<HTMLDivElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const formRef = useRef<HTMLFormElement>(null);
  const [errors, setErrors] = useState<Partial<Record<keyof HiringFormData, string>>>({});

  useEffect(() => {
    if (!sectionRef.current) return;

    const ctx = gsap.context(() => {
      // Heading animation
      gsap.fromTo(
        headingRef.current,
        {
          y: 50,
          opacity: 0,
        },
        {
          y: 0,
          opacity: 1,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: headingRef.current,
            start: "top 80%",
            toggleActions: "play none none reverse",
          },
        }
      );

      // Form animation
      gsap.fromTo(
        formContainerRef.current,
        {
          x: 50,
          opacity: 0,
        },
        {
          x: 0,
          opacity: 1,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: formContainerRef.current,
            start: "top 80%",
            toggleActions: "play none none reverse",
          },
        }
      );
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setErrors((prev) => ({ ...prev, "pdf-file": undefined }));
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const file = e.dataTransfer.files?.[0];
    if (file && file.type === "application/pdf") {
      setSelectedFile(file);
      setErrors((prev) => ({ ...prev, "pdf-file": undefined }));
    } else {
      setErrors((prev) => ({ ...prev, "pdf-file": "Only PDF files are allowed" }));
    }
  };

  const validateForm = (formData: FormData): boolean => {
    try {
      hiringFormSchema.parse({
        name: formData.get("name"),
        "value-add": formData.get("value-add"),
        "pdf-file": selectedFile,
      });
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Partial<Record<keyof HiringFormData, string>> = {};
        error.errors.forEach((err) => {
          const field = err.path[0] as keyof HiringFormData;
          newErrors[field] = err.message;
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const formData = new FormData(e.currentTarget);

      if (!validateForm(formData)) {
        setIsSubmitting(false);
        return;
      }

      const result = await sendHiringEmail(formData);

      if (result.success) {
        toast.success(result.message);
        formRef.current?.reset();
        setSelectedFile(null);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("An unexpected error occurred. Please try again later.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <main className="main flex flex-row items-center justify-between">
      <section className="w-full px-10 lg:w-1/2" id="1" ref={sectionRef}>
        <div className="block-content-register block-content-login">
          <div className="container px-4 pt-16 pb-4 lg:py-10">
            <div className="row">
              <div className="col-xl-6">
                <div className="form-login" ref={formContainerRef}>
                  <h4
                    ref={headingRef}
                    className="color-brand-2 heading-2 mb-10 pt-20 text-4xl font-bold text-[#1c5534] lg:mb-20 lg:text-6xl"
                  >
                    Hiring
                  </h4>
                  <h6 className="mb-10 text-gray-700 lg:mb-20">
                    Join our team at EPIC and be a part of turning great ideas into reality. We
                    believe in not just imagining ideas but also in executing them. While our vision
                    is ambitious, we're confident that with dedicated teamwork, we can significantly
                    contribute to the nationwide growth of padel in North America. Be a part of our
                    EPIC journey!
                  </h6>
                  <form ref={formRef} onSubmit={handleSubmit}>
                    <div className="form-group mb-6">
                      <input
                        className={`form-control font-helvetica w-full rounded-xl border ${
                          errors.name ? "border-red-500" : "border-gray-300"
                        } bg-white px-4 py-6 font-bold focus:border-transparent focus:ring-2 focus:ring-[#1c5534] focus:outline-none`}
                        type="text"
                        id="name"
                        name="name"
                        placeholder="Name"
                        required
                      />
                      {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
                    </div>
                    <div className="form-group mb-6">
                      <input
                        className={`form-control font-helvetica w-full rounded-xl border ${
                          errors["value-add"] ? "border-red-500" : "border-gray-300"
                        } bg-white px-4 py-6 font-bold focus:border-transparent focus:ring-2 focus:ring-[#1c5534] focus:outline-none`}
                        type="text"
                        id="value-add"
                        name="value-add"
                        placeholder="Where do you see adding value the most?"
                        required
                      />
                      {errors["value-add"] && (
                        <p className="mt-1 text-sm text-red-500">{errors["value-add"]}</p>
                      )}
                    </div>
                    <div className="form-group mb-6">
                      <input
                        className="form-control-file hidden"
                        type="file"
                        id="pdf-file"
                        name="pdf-file"
                        accept="application/pdf"
                        required
                        onChange={handleFileChange}
                      />
                      <div
                        id="drop-zone"
                        className={`flex h-32 cursor-pointer items-center justify-center rounded-xl border-2 border-dashed ${
                          errors["pdf-file"] ? "border-red-500" : "border-gray-300"
                        } bg-gray-50 p-6 text-center transition-colors hover:border-[#1c5534] hover:bg-gray-100`}
                        onClick={() => document.getElementById("pdf-file")?.click()}
                        onDragOver={handleDragOver}
                        onDrop={handleDrop}
                      >
                        {selectedFile ? (
                          <span className="text-[#1c5534]">{selectedFile?.name}</span>
                        ) : (
                          <span className="text-gray-500">Drag and drop your Resume here</span>
                        )}
                      </div>
                      {errors["pdf-file"] && (
                        <p className="mt-1 text-sm text-red-500">{errors["pdf-file"]}</p>
                      )}
                    </div>
                    <div className="form-group">
                      <button
                        className="btn btn-black mr-15 rounded-full bg-[#1c5534] px-8 py-4 text-xl font-bold text-white transition-colors duration-300 hover:bg-[#16432a] disabled:cursor-not-allowed disabled:opacity-50"
                        id="submit-btn"
                        type="submit"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? "Submitting..." : "Submit"}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section>
        <Image
          src="/imgs/page/login/banner.png"
          alt="Hiring"
          width={1000}
          height={1000}
          className="hidden object-cover lg:block"
          loading="lazy"
        />
      </section>
    </main>
  );
};

export default HiringPage;
