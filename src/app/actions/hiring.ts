"use server";

import { SESClient, SendRawEmailCommand } from "@aws-sdk/client-ses";
import { existsSync } from "fs";
import { mkdir, writeFile } from "fs/promises";
import { join } from "path";
import { z } from "zod";

const hiringFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  "value-add": z.string().min(10, "Please provide a detailed response about how you can add value"),
  "pdf-file": z.any().refine(
    (file) => {
      if (!file) return false;
      // Check if it's a File object (browser) or has the expected properties (server)
      return typeof file === "object" && "type" in file && file.type === "application/pdf";
    },
    {
      message: "Only PDF files are allowed",
    }
  ),
});

// Initialize SES client
const sesClient = new SESClient({
  region: process.env.AWS_REGION! || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID! || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY! || "",
  },
});

const generateEmailTemplate = (data: z.infer<typeof hiringFormSchema>) => `
<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f9;
            margin: 0;
            padding: 0;
            color: #333;
        }
        .container {
            width: 80%;
            margin: auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            background-color: #1c5534;
            color: white;
            padding: 10px;
            text-align: center;
            border-radius: 10px 10px 0 0;
        }
        .content {
            padding: 20px;
        }
        .footer {
            text-align: center;
            padding: 10px;
            background-color: #1c5534;
            color: white;
            border-radius: 0 0 10px 10px;
        }
        .footer a {
            color: #fff;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>Hiring Form Submission</h1>
        </div>
        <div class='content'>
            <p><strong>Name:</strong> ${data.name}</p>
            <p><strong>Value Add:</strong></p>
            <p>${data["value-add"]}</p>
            <p><strong>Resume:</strong> ${data["pdf-file"].name}</p>
        </div>
        <div class='footer'>
            <p>Thank you for your interest in joining EPIC!</p>
            <p><a href='https://epic-padel.com/'>Visit our website</a></p>
        </div>
    </div>
</body>
</html>
`;

async function saveFile(file: File): Promise<string> {
  try {
    // Create uploads directory if it doesn't exist
    const uploadDir = join(process.cwd(), "public", "uploads");
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const originalName = file.name;
    const fileExtension = originalName.split(".").pop();
    const newFileName = `${timestamp}-${originalName}`;
    const filePath = join(uploadDir, newFileName);

    // Convert File to Buffer and save
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    await writeFile(filePath, buffer);

    return filePath;
  } catch (error) {
    console.error("Error saving file:", error);
    throw new Error("Failed to save file");
  }
}

async function fileToBase64(file: File): Promise<string> {
  try {
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    return buffer.toString("base64");
  } catch (error) {
    console.error("Error converting file to base64:", error);
    throw new Error("Failed to process file");
  }
}

export async function sendHiringEmail(formData: FormData) {
  try {
    const validatedData = hiringFormSchema.parse({
      name: formData.get("name"),
      "value-add": formData.get("value-add"),
      "pdf-file": formData.get("pdf-file"),
    });

    const pdfFile = validatedData["pdf-file"] as File;
    const pdfBase64 = await fileToBase64(pdfFile);

    const emailParams = {
      Source: process.env.AWS_SES_FROM_EMAIL || "<EMAIL>",
      Destination: {
        ToAddresses: [process.env.AWS_SES_TO_EMAIL || "<EMAIL>"],
      },
      RawMessage: {
        Data: Buffer.from(
          `From: ${process.env.AWS_SES_FROM_EMAIL || "<EMAIL>"}\r\n` +
            `To: ${process.env.AWS_SES_TO_EMAIL || "<EMAIL>"}\r\n` +
            `Subject: Epic Padel - Hiring Form Submission\r\n` +
            `MIME-Version: 1.0\r\n` +
            `Content-Type: multipart/mixed; boundary="boundary"\r\n\r\n` +
            `--boundary\r\n` +
            `Content-Type: text/html; charset="UTF-8"\r\n\r\n` +
            `${generateEmailTemplate(validatedData)}\r\n\r\n` +
            `--boundary\r\n` +
            `Content-Type: application/pdf\r\n` +
            `Content-Disposition: attachment; filename="${pdfFile.name}"\r\n` +
            `Content-Transfer-Encoding: base64\r\n\r\n` +
            `${pdfBase64}\r\n\r\n` +
            `--boundary--`
        ),
      },
    };

    await sesClient.send(new SendRawEmailCommand(emailParams));

    return { success: true, message: "Application submitted successfully!" };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, message: error.errors[0].message };
    }
    console.error("Error sending email:", error);
    return { success: false, message: "Failed to submit application. Please try again later." };
  }
}
