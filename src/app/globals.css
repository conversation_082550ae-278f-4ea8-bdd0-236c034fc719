@import "tailwindcss";

:root {
  --background: #fffaef;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: #1b5632;
  --color-secondary: #ddba0a;
  --font-helvetica: var(--font-helvetica);
  --font-neue-haas: var(--font-neue-haas);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #fffaef;
    --foreground: #171717;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

.page-loader {
  position: fixed;
  width: 100vw;
  height: 100vh;
  z-index: 99999;
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  background-color: transparent;
}

.page-loader .page-loader-logo {
  display: inline;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  z-index: 100;
}

.bar {
  width: 10vw;
  height: 105vh;
  background: #1a1a1a;
  transform-origin: bottom;
}

/* Page transition styles */
.page-transition {
  position: relative;
}

.hide-animation {
  animation-name: fadeOut;
  animation-duration: 0.5s;
  animation-delay: 0.5s;
  animation-fill-mode: forwards;
}

/* Animation for page transitions */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes overlayShow {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes contentShow {
  from {
    opacity: 0;
    transform: translate(-50%, -48%) scale(0.96);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.overlayShow {
  animation: overlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
}

.contentShow {
  animation: contentShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
}

button {
  @apply cursor-pointer;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-7 {
  display: -webkit-box;
  -webkit-line-clamp: 7;
  line-clamp: 7;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-outline {
  -webkit-text-stroke: 1.2px black;
  color: transparent; /* or transparent */
}

#blog-detail-container .blog-content {
  font-family: "helvetica", sans-serif;
  font-size: 18px;
  line-height: 1.8;
  color: black;
}

#blog-detail-container .blog-content p {
  word-wrap: break-word; /* legacy but still works */
  overflow-wrap: break-word; /* modern preferred property */
  white-space: normal; /* ensures wrapping is allowed */
  margin-bottom: 20px;
}

#blog-detail-container .blog-content h2 {
  font-weight: 600;
  font-size: 24px;
}

#blog-detail-container .blog-content h3 {
  font-weight: 500;
  font-size: 20px;
}

#blog-detail-container .blog-content h4 {
  margin: 30px 0 15px;
  font-weight: 600;
}

#blog-detail-container .blog-content ul {
  margin-bottom: 20px;
  padding-left: 20px;
}

#blog-detail-container .blog-content ul li {
  margin-bottom: 10px;
  position: relative;
  padding-left: 20px;
}

#blog-detail-container .blog-content ul li:before {
  content: "";
  position: absolute;
  left: 0;
  top: 10px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #1c5534;
}

#location-description > a {
  @apply hover:bg-primary/90 font-helvetica bg-primary mt-2 inline-block w-fit rounded-full px-6 py-3 font-bold text-white transition-all;
}

.plan-benefit-details img {
  display: inline;
}

.plan-benefit-details span {
  word-wrap: break-all;
}

.plan-benefit-details p {
  word-wrap: break-all;
}

.plan-benefit-details li {
  position: relative;
  padding-left: 1.2rem;
  list-style: none;
}

.plan-benefit-details li::before {
  content: "✓";
  position: absolute;
  left: 0;
  top: 0;
  color: #16a34a;
  font-weight: bold;
}

.failed-message {
  word-wrap: break-all; /* legacy but still works */
  overflow-wrap: break-word; /* modern preferred property */
  white-space: normal; /* ensures wrapping is allowed */
}
