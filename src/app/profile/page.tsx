"use client";

import {
  FamilyMember,
  getFamilyMembers,
  openStripePortal,
  removeProfileImage,
  updateUserProfile,
  uploadProfileImage,
} from "@/api/profile-service";
import AddCorporateMemberDialog from "@/components/dialogs/add-corporate-member-dialog";
import AddMemberDialog from "@/components/dialogs/add-member-dialog";
import UpdateEmailDialog from "@/components/dialogs/update-email-dialog";
import UpdatePhoneDialog from "@/components/dialogs/update-phone-dialog";
import ProtectedLayout from "@/components/layouts/protected-layout";
import { modifyDate } from "@/libs/utils";
import { useAuthStore } from "@/store/auth-store";
import * as Dialog from "@radix-ui/react-dialog";
import { Divide, X } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";

const ProfilePage = () => {
  const { user, logout, updateUser } = useAuthStore();
  const router = useRouter();
  const [activeSection, setActiveSection] = useState("profile-section");
  const [editingField, setEditingField] = useState("");
  const [showEditModal, setShowEditModal] = useState(false);
  const [showOtpStep, setShowOtpStep] = useState(false);
  const [editFieldValue, setEditFieldValue] = useState("");
  const [phoneValue, setPhoneValue] = useState("");
  const [otpValues, setOtpValues] = useState(["", "", "", ""]);
  const [dobValue, setDobValue] = useState("");
  const [showButtons, setShowButtons] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showAddMemberDialog, setShowAddMemberDialog] = useState(false);
  const [showAddCorporateMemberDialog, setShowAddCorporateMemberDialog] = useState(false);
  const [showLogoutPage, setShowLogoutPage] = useState(false);
  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>([]);
  const [isLoadingMembers, setIsLoadingMembers] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [isDeletingImage, setIsDeletingImage] = useState(false);
  const [showPhoneDialog, setShowPhoneDialog] = useState(false);
  const [showEmailDialog, setShowEmailDialog] = useState(false);

  useEffect(() => {
    if (user) {
      setEditFieldValue(user.user_email?.email || "");
      setPhoneValue(user.user_phone?.phone || "");
      setDobValue(user.dob || "");
    }
  }, [user]);

  useEffect(() => {
    if (activeSection === "family-section") {
      loadFamilyMembers();
    }
  }, [activeSection]);

  const handleSectionClick = (sectionId: string) => {
    if (sectionId === "logout-section") {
      setShowLogoutPage(true);
    }
    setActiveSection(sectionId);
  };

  const handleLogout = () => {
    logout();
    router.push("/");
  };

  const openEditModal = (field: string) => {
    if (field === "phone") {
      setShowPhoneDialog(true);
      return;
    }
    if (field === "email") {
      setShowEmailDialog(true);
      return;
    }
    setEditingField(field);
    setShowEditModal(true);
    setShowOtpStep(false);
  };

  const closeEditModal = () => {
    setShowEditModal(false);
    setShowOtpStep(false);
    setOtpValues(["", "", "", ""]);
    setEditFieldValue("");
    setPhoneValue("");
  };

  const handleConfirmEdit = async () => {
    console.log(
      "Sending OTP for",
      editingField,
      editingField === "email" ? editFieldValue : phoneValue
    );
    setShowOtpStep(true);
  };

  const handleVerifyOtp = async () => {
    const otp = otpValues.join("");
    if (otp.length === 4) {
      // TODO: Implement API call to verify OTP
      console.log("Verifying OTP:", otp);
      closeEditModal();
    }
  };

  const handleOtpChange = (index: number, value: string) => {
    if (value.length <= 1) {
      const newOtpValues = [...otpValues];
      newOtpValues[index] = value;
      setOtpValues(newOtpValues);

      // Auto-focus next input
      if (value && index < 3) {
        const nextInput = document.querySelector(
          `input[data-otp-index="${index + 1}"]`
        ) as HTMLInputElement;
        if (nextInput) nextInput.focus();
      }
    }
  };

  const handleDobChange = (value: string) => {
    setDobValue(value);
    setShowButtons(true);
  };

  const handleSaveDob = async () => {
    try {
      // TODO: Implement API call to save DOB
      console.log("Saving DOB:", dobValue);
      const response = await updateUserProfile({ dob: dobValue });
      if (response) {
        toast.success(response.message || "DOB updated successfully!");
        updateUser({ dob: dobValue });
        setShowButtons(false);
      }
    } catch (error: any) {
      console.error("Error saving DOB:", error);
      toast.error(error?.errors?.dob?.[0] || "Failed to update DOB");
    }
  };

  const handleCancelDob = () => {
    setDobValue(user?.dob || "");
    setShowButtons(false);
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setIsUploadingImage(true);
      try {
        const response = await uploadProfileImage(file);
        updateUser({ profile_image: { path: response.user.profile_image.path } });
        toast.success(response.message || "Profile image uploaded successfully!");
      } catch (error: any) {
        console.error("Error uploading image:", error);
        toast.error(error.message || "Failed to upload image");
      } finally {
        setIsUploadingImage(false);
        event.target.value = "";
      }
    }
  };

  const handleDeleteImage = () => {
    setShowDeleteConfirm(true);
  };

  const confirmDeleteImage = async () => {
    setIsDeletingImage(true);
    try {
      const response = await removeProfileImage();
      if (response) {
        toast.success("Profile image deleted successfully!");
        updateUser({ profile_image: { path: "" } });
      }
      setShowDeleteConfirm(false);
    } catch (error: any) {
      toast.error(error.message || "Failed to delete image");
    } finally {
      setIsDeletingImage(false);
    }
  };

  const loadFamilyMembers = async () => {
    setIsLoadingMembers(true);
    try {
      const members = await getFamilyMembers();
      setFamilyMembers(members);
    } catch (error: any) {
      console.error("Error loading family members:", error);
      toast.error(error.message || "Failed to load family members");
    } finally {
      setIsLoadingMembers(false);
    }
  };

  const handleManagePlan = async () => {
    try {
      if (user?.userCurrentPackage?.is_default) {
        router.push("/membership");
        return;
      }
      await openStripePortal();
    } catch (error: any) {
      console.error("Error opening Stripe portal:", error);
      toast.error(error.message || "Failed to open manage plan portal");
    }
  };

  const handleAddMember = () => {
    if (user?.is_corporate_root_user) {
      setShowAddCorporateMemberDialog(true);
    }
    if (!user?.is_corporate_user) {
      setShowAddMemberDialog(true);
    }
  };

  return (
    <ProtectedLayout>
      <main className="bg-background mt-24 min-h-screen md:mt-32">
        <div className="mx-4 max-w-7xl rounded-lg bg-white px-4 py-8 shadow-sm sm:px-6 md:mx-auto lg:px-8">
          <h2 className="mb-6 text-2xl font-bold text-gray-900">Setting</h2>
          <div className="flex flex-col gap-8 md:flex-row">
            <aside className="w-full rounded-lg bg-white p-4 md:w-80">
              <div
                className={`mb-2 flex cursor-pointer items-center rounded-lg p-3 ${
                  activeSection === "profile-section"
                    ? "bg-primary/10 text-primary"
                    : "hover:bg-gray-50"
                }`}
                onClick={() => handleSectionClick("profile-section")}
              >
                <div className="mr-3 h-8 w-8">
                  <Image
                    src="/imgs/profile-lo.png"
                    alt="profile"
                    title="profile"
                    className="h-full w-full object-cover"
                    width={32}
                    height={32}
                    priority
                  />
                </div>
                <div>
                  <div className="font-medium">Profile</div>
                  <div className="text-sm text-gray-500">Add your personal information</div>
                </div>
              </div>

              <div
                className={`mb-2 flex cursor-pointer items-center rounded-lg p-3 ${
                  activeSection === "subscriptions-section"
                    ? "bg-primary/10 text-primary"
                    : "hover:bg-gray-50"
                }`}
                onClick={() => handleSectionClick("subscriptions-section")}
              >
                <div className="mr-3 h-8 w-8">
                  <Image
                    src="/imgs/subsc.png"
                    alt="subscriptions"
                    title="subscriptions"
                    className="h-full w-full object-cover"
                    width={32}
                    height={32}
                    priority
                  />
                </div>
                <div>
                  <div className="font-medium">My Subscriptions</div>
                  <div className="text-sm text-gray-500">Manage your subscriptions</div>
                </div>
              </div>

              <div
                className={`mb-2 flex cursor-pointer items-center rounded-lg p-3 ${
                  activeSection === "family-section"
                    ? "bg-primary/10 text-primary"
                    : "hover:bg-gray-50"
                }`}
                onClick={() => handleSectionClick("family-section")}
              >
                <div className="mr-3 h-8 w-8">
                  <Image
                    src="/imgs/profile-lo.png"
                    alt="family"
                    title="family"
                    className="h-full w-full object-cover"
                    width={32}
                    height={32}
                    priority
                  />
                </div>
                <div>
                  <div className="font-medium">{`${user?.is_corporate_user ? "Corporate Membership" : "Family Membership"}`}</div>
                  <div className="text-sm text-gray-500">{`${user?.is_corporate_user ? (user?.is_corporate_root_user ? "Add your corporate members" : "See your corporate members") : "Add your family members"}`}</div>
                </div>
              </div>

              <div
                className={`mb-2 flex cursor-pointer items-center rounded-lg p-3 ${
                  activeSection === "logout-section"
                    ? "bg-primary/10 text-primary"
                    : "hover:bg-gray-50"
                }`}
                onClick={() => handleSectionClick("logout-section")}
              >
                <div className="mr-3 h-8 w-8">
                  <Image
                    src="/imgs/logout.png"
                    alt="logout"
                    title="logout"
                    className="h-full w-full object-cover"
                    width={32}
                    height={32}
                    priority
                  />
                </div>
                <div>
                  <div className="font-medium">Logout</div>
                  <div className="text-sm text-gray-500">:(</div>
                </div>
              </div>
            </aside>

            {/* Profile Section */}
            <div
              className={`flex-1 rounded-lg bg-white p-6 ${
                activeSection === "profile-section" ? "" : "hidden"
              }`}
              id="profile-section"
            >
              <h2 className="mb-6 text-2xl font-bold text-gray-900">My Profile</h2>
              <div className="mb-8 flex flex-col items-start">
                <div className="relative">
                  <Image
                    src={user?.profile_image?.path || "/imgs/profile.png"}
                    alt="profile picture"
                    title="profile picture"
                    width={128}
                    height={128}
                    className="h-32 w-32 rounded-full object-cover"
                  />
                  <div className="absolute right-0 -bottom-2 flex gap-2">
                    {user?.profile_image?.path && (
                      <button
                        className={`rounded-full bg-white p-2 shadow-md hover:bg-gray-50 ${
                          isDeletingImage ? "cursor-not-allowed opacity-50" : ""
                        }`}
                        onClick={isDeletingImage ? undefined : handleDeleteImage}
                        disabled={isDeletingImage}
                      >
                        {isDeletingImage ? (
                          <div className="animate-spin">⟳</div>
                        ) : (
                          <Image
                            src="/imgs/delete.svg"
                            alt="delete"
                            title="delete"
                            width={20}
                            height={20}
                            className="h-5 w-5"
                          />
                        )}
                      </button>
                    )}
                    <button
                      className={`rounded-full bg-white p-2 shadow-md hover:bg-gray-50 ${
                        isUploadingImage ? "cursor-not-allowed opacity-50" : ""
                      }`}
                      onClick={
                        isUploadingImage
                          ? undefined
                          : () => document.getElementById("image-upload")?.click()
                      }
                      disabled={isUploadingImage}
                    >
                      {isUploadingImage ? (
                        <div className="animate-spin">⟳</div>
                      ) : (
                        <Image
                          src="/imgs/edit.svg"
                          alt="edit"
                          title="edit"
                          width={20}
                          height={20}
                          className="h-5 w-5"
                        />
                      )}
                    </button>
                  </div>
                </div>
                <input
                  type="file"
                  id="image-upload"
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageUpload}
                />
              </div>

              {/* Delete Confirmation Modal */}
              <Dialog.Root open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
                <Dialog.Portal>
                  <Dialog.Overlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[2000] bg-black/50" />
                  <Dialog.Content className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-[2001] w-full max-w-md translate-x-[-50%] translate-y-[-50%] rounded-[20px] bg-white p-6 shadow-lg duration-200">
                    <Dialog.Close className="focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-2 right-2 rounded-sm opacity-70 ring-0 transition-opacity hover:opacity-100 focus:ring-0 focus:outline-none disabled:pointer-events-none">
                      <X className="h-8 w-8 text-gray-600 outline-none" />
                      <span className="sr-only">Close</span>
                    </Dialog.Close>

                    <Dialog.Title className="text-lg font-medium text-gray-900">
                      Delete Profile Picture
                    </Dialog.Title>
                    <Dialog.Description className="mt-2 text-sm text-gray-500">
                      Are you sure you want to remove your profile picture?
                    </Dialog.Description>

                    <div className="mt-6 flex justify-end space-x-3">
                      <button
                        type="button"
                        className="focus:ring-primary rounded-full border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-offset-2 focus:outline-none"
                        onClick={() => setShowDeleteConfirm(false)}
                      >
                        Cancel
                      </button>
                      <button
                        type="button"
                        className="bg-primary hover:bg-primary/90 focus:ring-primary rounded-full border border-transparent px-4 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                        onClick={confirmDeleteImage}
                        disabled={isDeletingImage}
                      >
                        {isDeletingImage ? "Deleting..." : "Yes, Remove"}
                      </button>
                    </div>
                  </Dialog.Content>
                </Dialog.Portal>
              </Dialog.Root>

              <div className="space-y-6">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Email</label>
                  <div className="relative">
                    <input
                      type="email"
                      className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
                      value={user?.user_email?.email || ""}
                      readOnly
                      placeholder="Enter your email"
                    />
                    {!user?.user_email?.email && (
                      <Image
                        width={20}
                        height={20}
                        onClick={() => openEditModal("email")}
                        src="/imgs/edit.svg"
                        alt="edit"
                        title="edit"
                        className="absolute top-1/2 right-3 h-5 w-5 -translate-y-1/2 cursor-pointer"
                      />
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Phone Number</label>
                  <div className="relative">
                    <input
                      type="tel"
                      className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
                      value={
                        user?.user_phone
                          ? `${user.user_phone.country_code}${user.user_phone.phone}`
                          : ""
                      }
                      readOnly
                      placeholder="Enter Your Phone Number"
                    />
                    {!user?.user_phone && (
                      <Image
                        width={20}
                        height={20}
                        src="/imgs/edit.svg"
                        alt="edit"
                        title="edit"
                        onClick={() => openEditModal("phone")}
                        className="absolute top-1/2 right-3 h-5 w-5 -translate-y-1/2 cursor-pointer"
                      />
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Date Of Birth</label>
                  <input
                    type="date"
                    id="dob"
                    className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
                    value={dobValue}
                    onChange={(e) => handleDobChange(e.target.value)}
                    placeholder="Enter Your Date of Birth"
                  />
                </div>

                {showButtons && (
                  <div className="flex justify-end space-x-3">
                    <button
                      className="focus:ring-primary rounded-full border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-offset-2 focus:outline-none"
                      onClick={handleCancelDob}
                    >
                      Cancel
                    </button>
                    <button
                      className="bg-primary hover:bg-primary/90 focus:ring-primary rounded-full border border-transparent px-4 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none"
                      onClick={handleSaveDob}
                    >
                      Save
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Subscriptions Section */}
            <div
              className={`flex-1 rounded-lg bg-white p-6 ${
                activeSection === "subscriptions-section" ? "" : "hidden"
              }`}
              id="subscriptions-section"
            >
              <h2 className="mb-6 text-2xl font-bold text-gray-900">My Subscriptions</h2>
              <div className="space-y-6">
                <div className="rounded-lg border border-gray-200 bg-white p-6">
                  <div className="space-y-4">
                    <div>
                      <h5 className="text-sm font-medium text-gray-500">Plan Name</h5>
                      <p className="mt-1 text-lg font-semibold text-gray-900">
                        {user?.userCurrentPackage?.name || "No Plan"}
                      </p>
                    </div>
                    <div>
                      <h5 className="text-sm font-medium text-gray-500">Join Date</h5>
                      <p className="mt-1 text-lg font-semibold text-gray-900">
                        {modifyDate(user?.active_purchase_subscription?.start_date!)}
                      </p>
                    </div>
                    <div>
                      <h5 className="text-sm font-medium text-gray-500">Expire Date</h5>
                      <p className="mt-1 text-lg font-semibold text-gray-900">
                        {modifyDate(user?.active_purchase_subscription?.end_date!)}
                      </p>
                    </div>
                  </div>
                  <div className="mt-6">
                    {/* if user is corporate root user */}
                    {(user?.is_corporate_root_user ||
                      (!user?.is_corporate_root_user && !user?.is_corporate_user) ||
                      user?.userCurrentPackage?.is_default) && (
                      <button
                        className="bg-primary hover:bg-primary/90 focus:ring-primary w-fit rounded-full border border-transparent px-4 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none"
                        onClick={handleManagePlan}
                      >
                        Manage Plan
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Family Section */}
            <div
              className={`flex-1 rounded-lg bg-white p-6 ${
                activeSection === "family-section" ? "" : "hidden"
              }`}
              id="family-section"
            >
              <div className="mb-6 flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-900">{` ${user?.is_corporate_user ? "Corporate Membership" : "Family Membership"}`}</h2>
                {((!user?.is_corporate_root_user && !user?.is_corporate_user) ||
                  user?.is_corporate_root_user) && (
                  <button
                    className="bg-primary hover:bg-primary/90 focus:ring-primary rounded-full border border-transparent px-4 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                    onClick={handleAddMember}
                    disabled={
                      isLoadingMembers ||
                      user?.userCurrentPackage?.is_default ||
                      (user?.is_corporate_root_user &&
                        familyMembers.length >= user?.userCurrentPackage?.corporate_email_limit!)
                    }
                  >
                    Add Member
                  </button>
                )}
              </div>

              {user?.is_corporate_root_user &&
                familyMembers.length >= user?.userCurrentPackage?.corporate_email_limit! && (
                  <div className="my-2 text-sm font-medium text-red-600">
                    You have reached the maximum number of members allowed for your plan.
                  </div>
                )}

              {isLoadingMembers ? (
                <div className="space-y-4">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <div key={index} className="animate-pulse">
                      <div className="h-24 rounded-lg bg-gray-200"></div>
                    </div>
                  ))}
                </div>
              ) : familyMembers.length === 0 ? (
                <div className="py-12 text-center">
                  <p className="text-gray-500">
                    {user?.userCurrentPackage?.is_default
                      ? "Please upgrade your membership to add members"
                      : "You don't have any members yet"}
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {familyMembers.map((member) => (
                    <div key={member.id} className="rounded-lg border border-gray-200 bg-white p-6">
                      <div className="flex justify-between gap-4">
                        <div className="min-w-30 lg:min-w-50">
                          <div className="text-sm font-medium text-gray-500">Name</div>
                          <div className="mt-1 truncate text-lg font-semibold text-gray-900 capitalize">
                            {member?.user?.name || "N/A"}
                          </div>
                        </div>
                        {!user?.is_corporate_user && (
                          <div>
                            <div className="text-sm font-medium text-gray-500">Amount Paid</div>
                            <div className="mt-1 text-lg font-semibold text-gray-900">
                              ${member?.amount_paid || "N/A"}
                            </div>
                          </div>
                        )}
                        <div>
                          <div className="text-sm font-medium text-gray-500">Coverage Date</div>
                          <div className="mt-1 text-lg font-semibold text-gray-900">
                            {modifyDate(member.start_date)} - {modifyDate(member.end_date)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Logout Section */}
            <div
              className={`flex-1 rounded-lg bg-white p-6 ${
                activeSection === "logout-section" ? "" : "hidden"
              }`}
              id="logout-section"
            >
              <h2 className="mb-6 text-2xl font-bold text-gray-900">Logout</h2>
              <p className="mb-6 text-gray-500">Are you sure you want to logout?</p>
              <button
                className="bg-primary hover:bg-primary/90 focus:ring-primary rounded-full border border-transparent px-4 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none"
                onClick={handleLogout}
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </main>

      {/* Edit Modal */}
      {showEditModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-center justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div className="inline-block transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="mb-4 flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">Edit Email</h3>
                  <button className="text-gray-400 hover:text-gray-500" onClick={closeEditModal}>
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>

                {!showOtpStep ? (
                  <div className="space-y-4">
                    <input
                      type="email"
                      value={editFieldValue}
                      onChange={(e) => setEditFieldValue(e.target.value)}
                      placeholder="Enter new email"
                      className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
                    />
                  </div>
                ) : (
                  <div className="space-y-4">
                    <p className="text-sm text-gray-500">
                      Enter the 4-digit OTP sent to your email
                    </p>
                    <div className="flex justify-center space-x-2">
                      {otpValues.map((value, index) => (
                        <input
                          key={index}
                          type="text"
                          maxLength={1}
                          value={value}
                          onChange={(e) => handleOtpChange(index, e.target.value)}
                          className="focus:ring-primary h-12 w-12 rounded-full border border-gray-300 text-center text-lg focus:border-transparent focus:ring-2 focus:outline-none"
                          data-otp-index={index}
                        />
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                <button
                  type="button"
                  className="bg-primary hover:bg-primary/90 focus:ring-primary inline-flex w-full justify-center rounded-full border border-transparent px-4 py-2 text-base font-medium text-white shadow-sm focus:ring-2 focus:ring-offset-2 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={showOtpStep ? handleVerifyOtp : handleConfirmEdit}
                >
                  {showOtpStep ? "Verify OTP" : "Confirm"}
                </button>
                <button
                  type="button"
                  className="focus:ring-primary mt-3 inline-flex w-full justify-center rounded-full border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-offset-2 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={closeEditModal}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Member Dialog */}
      <AddMemberDialog
        isOpen={showAddMemberDialog}
        onClose={() => setShowAddMemberDialog(false)}
        onMemberAdded={loadFamilyMembers}
      />

      <AddCorporateMemberDialog
        isOpen={showAddCorporateMemberDialog}
        onClose={() => setShowAddCorporateMemberDialog(false)}
        onMemberAdded={loadFamilyMembers}
      />

      {/* Update Phone Dialog */}
      <UpdatePhoneDialog
        isOpen={showPhoneDialog}
        onClose={() => setShowPhoneDialog(false)}
        onPhoneUpdated={() => {
          // TODO: Implement phone update logic
          console.log("Phone updated");
        }}
        currentPhone={user?.user_phone?.phone || ""}
        currentCountryCode={user?.user_phone?.country_code || "+1"}
      />

      {/* Update Email Dialog */}
      <UpdateEmailDialog
        isOpen={showEmailDialog}
        onClose={() => setShowEmailDialog(false)}
        onEmailUpdated={() => {
          // TODO: Implement email update logic
          console.log("Email updated");
        }}
        currentEmail={user?.user_email?.email || ""}
      />
    </ProtectedLayout>
  );
};

export default ProfilePage;
