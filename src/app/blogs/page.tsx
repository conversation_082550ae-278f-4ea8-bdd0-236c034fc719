import { endpoints } from "@/api/axios";
import Blog<PERSON>ard from "@/components/blogs/blog-card";

interface BlogItem {
  id: number;
  slug: string;
  title: string;
  description: string;
  content?: string;
  cover_image?: string;
  banner_image?: string;
  cover_image_alt?: string;
  created_at: string;
  meta_keywords?: string;
}

interface BlogApiResponse {
  data: {
    data: BlogItem[];
  };
}

async function getBlogs() {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL!}${endpoints.public.blog.list}`,
      { next: { revalidate: 3600 } }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch blogs");
    }

    const data: BlogApiResponse = await response.json();
    return data.data.data;
  } catch (error) {
    console.error("Error fetching blog data:", error);
    return [];
  }
}

const BlogPage = async () => {
  const blogs = await getBlogs();

  return (
    <div className="container mx-auto px-4 py-16 pt-20 md:pt-32">
      <div className="mb-10 text-center">
        <h1 className="text-primary mb-2 text-4xl font-bold tracking-tight lg:text-6xl">Blogs</h1>
        <h2 className="heading-5 color-900 text-lg md:text-xl">
          Insights, tips, and stories from the world of padel
        </h2>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3">
        {blogs.length > 0 ? (
          blogs.map((blog) => <BlogCard key={blog.id} blog={blog} />)
        ) : (
          <div className="col-span-full py-12 text-center">
            <p className="text-lg text-gray-600">No blogs available at this time.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default BlogPage;
