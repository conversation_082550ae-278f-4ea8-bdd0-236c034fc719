// TypeScript interfaces for the plan data structure
export interface PackagePrice {
    id: number;
    frequency: "M" | "Y";
    amount: number;
    initial_amount?: number;
    iteration_offer?: number;
    is_on_offer?: boolean;
    limited_members?: number;
}

export interface PackageBenefit {
    id: number;
    name: string;
}

export interface Plan {
    id: number;
    name: string;
    description: string;
    package_prices: PackagePrice[];
    package_benefits: PackageBenefit[];
    one_time_price: string;
    is_default: boolean;
    min_age?: number;
    max_age?: number;
    benefit_details?: string;
}

export interface PlansResponse {
    data: Plan[];
}

export interface User {
    user?: {
        userCurrentPackage?: {
            id: number;
        };
    };
}