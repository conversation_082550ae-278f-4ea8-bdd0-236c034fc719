import { useAuthStore } from "@/store/auth-store";

/**
 * Utility function to logout user and redirect to home page
 */
export const logoutUser = () => {
  const { logout } = useAuthStore.getState();
  logout();
  
  // Redirect to home page
  if (typeof window !== "undefined") {
    window.location.href = "/";
  }
};

/**
 * Check if user is authenticated
 */
export const isUserAuthenticated = (): boolean => {
  const { isAuthenticated } = useAuthStore.getState();
  return isAuthenticated;
};

/**
 * Get current user data
 */
export const getCurrentUser = () => {
  const { user } = useAuthStore.getState();
  return user;
};
