export interface NavItem {
  label: string;
  href?: string;
  children?: NavItem[];
}

export const navigationItems: NavItem[] = [
  {
    label: "Hello",
    children: [
      { label: "Contact Us", href: "/contact" },
      { label: "Hiring", href: "/hiring" },
    ],
  },
  {
    label: "We Are Epic",
    children: [
      { label: "About Us", href: "/about" },
      { label: "Epic Team", href: "/team" },
      { label: "Blogs", href: "/blogs" },
    ],
  },
  {
    label: "Find Us",
    children: [
      { label: "Charlotte, NC", href: "/locations/charlotte" },
      { label: "Salt Lake City, UT", href: "/locations/utah" },
      { label: "Tyson's Corner, VA", href: "/locations/virginia" },
    ],
  },
  {
    label: "Join Us",
    children: [{ label: "Membership", href: "/membership" }],
  },
  {
    label: "Investments",
    children: [
      { label: "Investments", href: "/investments" },
      { label: "Incubation", href: "/incubation" },
    ],
  },
  {
    label: "Epiholic",
    children: [{ label: "Store", href: "/store" }],
  },
];
