import { addF<PERSON>ly<PERSON><PERSON>ber, type AddMemberData } from "@/api/profile-service";
import { useGetPaymentMethods, setDefaultPaymentMethod } from "@/api/payment-service";
import { extractErrorMessage } from "@/libs/utils";
import React, { useState, useCallback, useMemo, useEffect } from "react";
import { toast } from "sonner";
import { z } from "zod";
import Loader from "../loader";
import { Skeleton } from "../ui/skeleton";
import PaymentFormSkeleton from "../payments/payment-form-skeleton";
import ValidationError from "./validation-error";

// Payment method interface based on API usage
interface PaymentMethod {
  id: string;
  brand: string;
  last_number: string;
  is_default: boolean;
}

// Zod validation schema
const memberFormSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .min(2, "Name must be at least 2 characters")
    .max(100, "Name must be less than 100 characters"),

  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address")
    .max(254, "Email must be less than 254 characters"),

  dob: z
    .string()
    .min(1, "Date of birth is required")
    .refine((date) => {
      const birthDate = new Date(date);
      const today = new Date();
      const minDate = new Date("1900-01-01");
      return birthDate >= minDate && birthDate < today;
    }, "Please enter a valid date of birth"),

  phone: z
    .string()
    .min(1, "Phone number is required")
    .regex(/^\d{10}$/, "Phone number must be exactly 10 digits"),

  country: z.string().min(1, "Country code is required"),

  paymentMethodId: z.string().min(1, "Payment method is required"),

  makeDefault: z.boolean(),
});

export type MemberFormData = z.infer<typeof memberFormSchema>;

interface AddFamilyMemberFormProps {
  onSuccess: () => void;
  onCancel: () => void;
  onShowPaymentForm: () => void;
  initialFormData?: MemberFormData;
  onFormDataChange?: (data: MemberFormData) => void;
}

// Form field errors type
type FormErrors = Partial<Record<keyof MemberFormData, string>>;

const AddFamilyMemberForm: React.FC<AddFamilyMemberFormProps> = ({
  onSuccess,
  onCancel,
  onShowPaymentForm,
  initialFormData,
  onFormDataChange,
}) => {
  const [isAddingMember, setIsAddingMember] = useState(false);
  const [memberFormData, setMemberFormData] = useState<MemberFormData>(
    initialFormData || {
      name: "",
      email: "",
      dob: "",
      phone: "",
      country: "+1",
      paymentMethodId: "",
      makeDefault: false,
    }
  );
  const [errors, setErrors] = useState<FormErrors>({});

  const { paymentMethodList, paymentMethodLoading } = useGetPaymentMethods();

  // Memoized typed payment methods list
  const typedPaymentMethods = useMemo((): PaymentMethod[] => {
    return (paymentMethodList || []) as PaymentMethod[];
  }, [paymentMethodList]);

  // Memoized default payment method
  const defaultPaymentMethod = useMemo(() => {
    return typedPaymentMethods.find((method) => method.is_default);
  }, [typedPaymentMethods]);

  // Memoized selected payment method
  const selectedPaymentMethod = useMemo(() => {
    if (!memberFormData.paymentMethodId) return null;
    // console.log("memberFormData.paymentMethodId", memberFormData.paymentMethodId);
    return typedPaymentMethods.find(
      (method) => String(method.id) === memberFormData.paymentMethodId
    );
  }, [typedPaymentMethods, memberFormData.paymentMethodId]);

  // Memoized check if selected method is default
  const isSelectedMethodDefault = useMemo(() => {
    // console.log("selectedPaymentMethod?.is_default", selectedPaymentMethod?.is_default);
    return selectedPaymentMethod?.is_default || false;
  }, [selectedPaymentMethod]);

  // Effect to sync with parent's initial form data
  useEffect(() => {
    if (initialFormData) {
      setMemberFormData(initialFormData);
    }
  }, [initialFormData]);

  // Effect to set default payment method when payment methods are loaded
  useEffect(() => {
    if (defaultPaymentMethod && !memberFormData.paymentMethodId) {
      const newFormData = {
        ...memberFormData,
        paymentMethodId: String(defaultPaymentMethod.id),
        makeDefault: true,
      };
      setMemberFormData(newFormData);

      // Update parent component's form data
      onFormDataChange?.(newFormData);
    }
  }, [defaultPaymentMethod, memberFormData.paymentMethodId, memberFormData, onFormDataChange]);

  // Validate entire form
  const validateForm = useCallback((): boolean => {
    try {
      memberFormSchema.parse(memberFormData);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: FormErrors = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            newErrors[err.path[0] as keyof MemberFormData] = err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  }, [memberFormData]);

  // Optimized form field update handler
  const updateFormField = useCallback(
    <K extends keyof MemberFormData>(field: K, value: MemberFormData[K]) => {
      const newFormData = { ...memberFormData, [field]: value };
      setMemberFormData(newFormData);

      // Update parent component's form data
      onFormDataChange?.(newFormData);

      // Clear error for this field if it exists
      if (errors[field]) {
        setErrors((prev) => ({ ...prev, [field]: undefined }));
      }
    },
    [errors, memberFormData, onFormDataChange]
  );

  // Handler for payment method change
  const handlePaymentMethodChange = useCallback(
    (paymentMethodId: string) => {
      const selectedMethod = typedPaymentMethods.find((method) => method.id === paymentMethodId);
      const isCurrentDefault = selectedMethod?.is_default || false;

      const newFormData = {
        ...memberFormData,
        paymentMethodId,
        makeDefault: isCurrentDefault,
      };

      setMemberFormData(newFormData);

      // Update parent component's form data
      onFormDataChange?.(newFormData);

      // Clear payment method error
      if (errors.paymentMethodId) {
        setErrors((prev) => ({ ...prev, paymentMethodId: undefined }));
      }
    },
    [typedPaymentMethods, errors.paymentMethodId, memberFormData, onFormDataChange]
  );

  // Optimized phone change handler
  const handlePhoneChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value.replace(/\D/g, "");
      if (value.length <= 10) {
        updateFormField("phone", value);
      }
    },
    [updateFormField]
  );

  // Reset form to initial state
  const resetForm = useCallback(() => {
    const newFormData = {
      name: "",
      email: "",
      dob: "",
      phone: "",
      country: "+1",
      paymentMethodId: defaultPaymentMethod?.id || "",
      makeDefault: defaultPaymentMethod?.is_default || false,
    };
    setMemberFormData(newFormData);

    // Update parent component's form data
    onFormDataChange?.(newFormData);

    setErrors({});
  }, [defaultPaymentMethod, onFormDataChange]);

  // Optimized form submission handler
  const handleMemberFormSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      // Validate form before submission
      if (!validateForm()) {
        // toast.error("Please fix the errors in the form");
        return;
      }

      setIsAddingMember(true);

      try {
        // Prepare data for API (matching AddMemberData interface)
        const memberData: AddMemberData = {
          name: memberFormData.name,
          email: memberFormData.email,
          phone: memberFormData.phone,
          country: memberFormData.country,
          dob: memberFormData.dob,
          payment_method_id: memberFormData.paymentMethodId,
        };

        const response = await addFamilyMember(memberData);

        // Set default payment method if requested and not already default
        if (
          memberFormData.makeDefault &&
          memberFormData.paymentMethodId &&
          !isSelectedMethodDefault
        ) {
          await setDefaultPaymentMethod({ paymentMethodId: memberFormData.paymentMethodId });
        }

        if (response) {
          toast.success("Family member added successfully!");
          resetForm();
          onSuccess();
        }
      } catch (error: any) {
        console.error("Error adding member:", error);
        const errorMessage = extractErrorMessage(error);
        toast.error(errorMessage || "Failed to add family member");
      } finally {
        setIsAddingMember(false);
      }
    },
    [memberFormData, validateForm, isSelectedMethodDefault, resetForm, onSuccess]
  );

  // Memoized max date for date input
  const maxDate = useMemo(() => {
    return new Date(Date.now() - 86400000).toISOString().split("T")[0];
  }, []);

  if (paymentMethodLoading) {
    return <PaymentFormSkeleton />;
  }

  return (
    <form onSubmit={handleMemberFormSubmit} className="space-y-6">
      <div className="grid grid-cols-1 gap-3 md:grid-cols-2 md:gap-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">Name</label>
          <input
            type="text"
            className={`focus:ring-primary w-full rounded-full border px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none ${
              errors.name ? "border-red-500" : "border-gray-300"
            }`}
            value={memberFormData.name}
            onChange={(e) => updateFormField("name", e.target.value)}
            placeholder="Enter Full Name"
            disabled={isAddingMember}
            aria-invalid={!!errors.name}
            aria-describedby={errors.name ? "name-error" : undefined}
          />
          {errors.name && <ValidationError id="name-error" error={errors.name} />}
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">Email</label>
          <input
            type="email"
            className={`focus:ring-primary w-full rounded-full border px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none ${
              errors.email ? "border-red-500" : "border-gray-300"
            }`}
            value={memberFormData.email}
            onChange={(e) => updateFormField("email", e.target.value)}
            placeholder="Enter Email"
            disabled={isAddingMember}
            aria-invalid={!!errors.email}
            aria-describedby={errors.email ? "email-error" : undefined}
          />
          {errors.email && <ValidationError id="email-error" error={errors.email} />}
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">Date of Birth</label>
          <input
            type="date"
            className={`focus:ring-primary w-full rounded-full border px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none ${
              errors.dob ? "border-red-500" : "border-gray-300"
            }`}
            value={memberFormData.dob}
            onChange={(e) => updateFormField("dob", e.target.value)}
            placeholder="Enter Date of Birth"
            min="1900-01-01"
            max={maxDate}
            disabled={isAddingMember}
            aria-invalid={!!errors.dob}
            aria-describedby={errors.dob ? "dob-error" : undefined}
          />
          {errors.dob && <ValidationError id="dob-error" error={errors.dob} />}
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">Phone Number</label>
          <div
            className={`flex overflow-hidden rounded-full border ${
              errors.phone ? "border-red-500" : "border-gray-300"
            }`}
          >
            <select
              value={memberFormData.country}
              onChange={(e) => updateFormField("country", e.target.value)}
              className="border-r border-gray-300 bg-gray-50 px-3 py-2 text-sm focus:outline-none"
              disabled={isAddingMember}
            >
              <option value="+1">🇺🇸 +1</option>
            </select>
            <input
              type="tel"
              placeholder="Mobile Number"
              value={memberFormData.phone}
              onChange={handlePhoneChange}
              maxLength={10}
              className="flex-1 px-3 py-2 text-black focus:outline-none"
              disabled={isAddingMember}
              aria-invalid={!!errors.phone}
              aria-describedby={errors.phone ? "phone-error" : undefined}
            />
          </div>
          {errors.phone && <ValidationError id="phone-error" error={errors.phone} />}
        </div>

        <div className="space-y-2 md:col-span-2">
          <label className="block text-sm font-medium text-gray-700">Payment Method</label>

          {paymentMethodLoading ? (
            <Skeleton className="h-10 w-full rounded-full" />
          ) : (
            <select
              className={`focus:ring-primary w-full rounded-full border px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none ${
                errors.paymentMethodId ? "border-red-500" : "border-gray-300"
              }`}
              value={memberFormData.paymentMethodId}
              onChange={(e) => handlePaymentMethodChange(String(e.target.value))}
              disabled={isAddingMember || paymentMethodLoading}
              aria-invalid={!!errors.paymentMethodId}
              aria-describedby={errors.paymentMethodId ? "payment-error" : undefined}
            >
              {!typedPaymentMethods.length && <option value="">Select Payment Method</option>}
              {typedPaymentMethods.map((method) => (
                <option key={method.id} value={String(method.id)}>
                  {method.brand?.toUpperCase()} **** **** **** {method.last_number}
                </option>
              ))}
            </select>
          )}
          {errors.paymentMethodId && (
            <ValidationError id="payment-error" error={errors.paymentMethodId} />
          )}
          <div className="flex w-full items-center justify-center">
            <button
              type="button"
              className="text-primary hover:text-primary mt-2 text-sm underline underline-offset-2 focus:outline-none"
              onClick={onShowPaymentForm}
              disabled={isAddingMember}
            >
              + Add New Payment Method
            </button>
          </div>
        </div>

        <div className="md:col-span-2">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              className="focus:ring-primary text-primary h-4 w-4 rounded border-gray-300"
              checked={memberFormData.makeDefault || isSelectedMethodDefault}
              onChange={(e) => updateFormField("makeDefault", e.target.checked)}
              disabled={
                isAddingMember || !memberFormData.paymentMethodId || isSelectedMethodDefault
              }
            />
            <span
              className={`text-sm ${isSelectedMethodDefault ? "text-gray-500" : "text-gray-700"}`}
            >
              {isSelectedMethodDefault
                ? "This is already your default payment method"
                : "Make this the default payment method"}
            </span>
          </label>
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          className="focus:ring-primary rounded-full border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-offset-2 focus:outline-none"
          onClick={onCancel}
          disabled={isAddingMember}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="bg-primary hover:bg-primary/90 focus:ring-primary rounded-full border border-transparent px-4 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
          disabled={isAddingMember}
        >
          {isAddingMember ? <Loader className="px-4" /> : "Add Member"}
        </button>
      </div>
    </form>
  );
};

export default AddFamilyMemberForm;
