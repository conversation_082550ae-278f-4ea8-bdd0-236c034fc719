"use client";

import React, { useEffect } from "react";
import { useAuthStore } from "@/store/auth-store";

interface AuthProviderProps {
  children: React.ReactNode;
}

const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { initializeAuth, checkAuth } = useAuthStore();

  useEffect(() => {
    // Initialize auth state from localStorage
    initializeAuth();
    
    // Check auth status with server
    checkAuth();
  }, [initializeAuth, checkAuth]);

  return <>{children}</>;
};

export default AuthProvider;
