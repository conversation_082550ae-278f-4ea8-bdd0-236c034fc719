import { cn } from "@/libs/utils";
import Link from "next/link";
import React from "react";

interface PrimaryLinkButtonProps {
  href: string;
  text: string;
  className?: string;
}

const PrimaryLinkButton = ({ href, text, className }: PrimaryLinkButtonProps) => {
  return (
    <Link
      className={cn(
        "text-primary font-helvetica inline-block cursor-pointer rounded-full bg-[#ddba0a] px-8 py-4 text-lg font-bold shadow-lg transition-all duration-300 ease-out hover:scale-105 hover:bg-[#ddba12] hover:shadow-xl sm:px-10 sm:py-4 sm:text-xl md:px-12 md:py-5 md:text-2xl",
        className
      )}
      href={href || "/"}
    >
      {text || "Button"}
    </Link>
  );
};

export default PrimaryLinkButton;
