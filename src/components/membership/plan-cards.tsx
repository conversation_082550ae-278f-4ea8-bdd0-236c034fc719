import { sendConversionAPIEvent } from "@/api/meta-conversion-api-service";
import { useFacebookPixel } from "@/hooks/use-facebook-pixel";
import { useUIStore } from "@/store/ui-store";
import { Plan, User } from "@/types/membership";
import PlanCardItem from "./plan-card-item";

// PlanCards Component
interface PlanCardsProps {
  plans: Plan[];
  isYearly: boolean;
  user: User | null;
}

export const PlanCards: React.FC<PlanCardsProps> = ({ plans, isYearly, user }) => {
  const { openLoginDialog, openMembershipDialog } = useUIStore();
  const { trackInitiateCheckout } = useFacebookPixel();
  const isUserHavePlan = user?.user?.userCurrentPackage?.id;

  const sortedPlans = [...plans].sort((a, b) => {
    return a.name === "Pay-to-Play" ? 1 : b.name === "Pay-to-Play" ? -1 : 0;
  });

  const handleSubscribe = (plan: Plan, price: number) => {
    if (plan.is_default) {
      return;
    }

    const token = localStorage.getItem("token");

    if (!token) {
      openLoginDialog();
      localStorage.setItem("planId", plan.id.toString());
      localStorage.setItem("planName", plan.name);
      localStorage.setItem("planPrice", price.toString());
      localStorage.setItem("planIsYearly", isYearly.toString());
      return;
    }

    openMembershipDialog({
      id: plan.id,
      name: plan.name,
      price: price,
      isYearly: isYearly,
      min_age: plan.min_age,
      max_age: plan.max_age,
    });

    trackInitiateCheckout(plan.id.toString(), price, plan.name, isYearly ? "Yearly" : "Monthly");
    // sendFacebookConversionApiEvent("InitiateCheckout", {
    //   email: "<EMAIL>",
    //   phone: "0123123123",
    // });
    sendConversionAPIEvent({ eventName: "InitiateCheckout" });
  };

  return (
    <>
      {sortedPlans.map((plan) => {
        const priceObj = isYearly
          ? plan.package_prices.find((p) => p.frequency === "M")
          : plan.package_prices.find((p) => p.frequency === "Y");

        const price = priceObj ? priceObj.amount : 0;

        const initialAmount = priceObj?.initial_amount || 0;
        const iterationOffer = priceObj?.iteration_offer || 0;
        const actualAmount = priceObj?.amount || 0;
        const isOnOffer = priceObj?.is_on_offer || false;
        const limitedMembers = priceObj?.limited_members || 50;

        const founding = Boolean(
          plan.id === 2 ||
            isOnOffer ||
            (!isYearly && plan.package_prices.find((p) => p.frequency === "M")?.is_on_offer)
        );

        const foundingMonthlyPriceInAnnualObj = plan.package_prices.find(
          (p) => p.frequency === "M" && p.is_on_offer
        );

        const foundingMonthlyPriceInAnnual = actualAmount / 12;
        const foundingAnnualActualAmount = foundingMonthlyPriceInAnnualObj
          ? foundingMonthlyPriceInAnnualObj.amount
          : 0;

        const foundingAnnualItterationOffer = foundingMonthlyPriceInAnnualObj
          ? foundingMonthlyPriceInAnnualObj.iteration_offer || 0
          : 0;

        return (
          <PlanCardItem
            key={plan.id}
            plan={plan}
            price={price}
            isYearly={isYearly}
            founding={founding}
            initialAmount={initialAmount}
            iterationOffer={iterationOffer}
            actualAmount={actualAmount}
            limitedMembers={limitedMembers}
            foundingMonthlyPriceInAnnual={foundingMonthlyPriceInAnnual}
            foundingAnnualActualAmount={foundingAnnualActualAmount}
            foundingAnnualItterationOffer={foundingAnnualItterationOffer}
            isUserHavePlan={isUserHavePlan}
            onSubscribe={handleSubscribe}
          />
        );
      })}
    </>
  );
};
