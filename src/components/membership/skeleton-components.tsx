// Skeleton Components
export const SkeletonCard = ({ isFoundingInner = false }: { isFoundingInner?: boolean }) => {
  const benefitWidths = ["w-32", "w-40", "w-36", "w-44", "w-28"];

  return (
    <div
      className={`flex max-w-[280px] flex-col rounded-3xl bg-white p-5 text-black ${
        isFoundingInner ? "mt-0" : "mt-9"
      } animate-pulse`}
    >
      {/* Plan Name Skeleton */}
      <div className="mb-4 h-8 w-24 rounded-md bg-gray-200"></div>

      {/* Price Skeleton */}
      <div className="mb-4">
        <div className="flex items-center justify-start gap-2.5">
          <div className="h-10 w-20 rounded bg-gray-200"></div>
          <div className="h-4 w-16 rounded bg-gray-200"></div>
        </div>
      </div>

      {/* Founding Member Details Skeleton (only for founding card) */}
      {isFoundingInner && (
        <div className="mb-4">
          <div className="h-4 w-40 rounded bg-gray-200"></div>
        </div>
      )}

      {/* Initiation Fee Skeleton */}
      <div className="mb-6">
        <div className="h-4 w-48 rounded bg-gray-200"></div>
      </div>

      {/* Benefits List Skeleton */}
      <ul className="mb-6 flex-1 space-y-2">
        {[1, 2, 3, 4, 5].map((item) => (
          <li key={item} className="flex items-start gap-2">
            <div className="mt-0.5 h-4 w-4 flex-shrink-0 rounded bg-gray-200"></div>
            <div className={`h-4 bg-gray-200 ${benefitWidths[item - 1]} rounded`}></div>
          </li>
        ))}
      </ul>

      {/* Button Skeleton */}
      <div className="h-12 w-full rounded-full bg-gray-200"></div>
    </div>
  );
};

export const SkeletonFoundingCard = () => {
  return (
    <div
      className="animate-pulse rounded-3xl px-0.5 pt-2.5 pb-0.5"
      style={{
        background:
          "linear-gradient(260deg, rgba(221, 186, 10, 0.3) 0%, rgba(255, 238, 150, 0.3) 100%)",
      }}
    >
      <div className="mx-auto mb-2 h-6 w-48 rounded bg-gray-300"></div>
      <SkeletonCard isFoundingInner={true} />
    </div>
  );
};

export const SkeletonCards = () => {
  return (
    <>
      {/* Regular skeleton cards */}
      <SkeletonFoundingCard />
      <SkeletonCard />
      <SkeletonCard />
      {/* Founding member skeleton card */}

      {/* More regular skeleton cards */}
      <SkeletonCard />
      <SkeletonCard />
    </>
  );
};
