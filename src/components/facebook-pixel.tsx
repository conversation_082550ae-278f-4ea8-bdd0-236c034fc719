"use client";

import Image from "next/image";
import { usePathname } from "next/navigation";
import { useEffect } from "react";

declare global {
  interface Window {
    fbq: any;
    _fbq: any;
  }
}

const FacebookPixel = () => {
  const pathname = usePathname();
  const pixelId = process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID!;

  useEffect(() => {
    // Initialize Facebook Pixel
    if (!window.fbq) {
      const f = window;
      const b = document;
      const e = "script";
      const v = "https://connect.facebook.net/en_US/fbevents.js";
      let n: any, t: any, s: any;

      if (f.fbq) return;
      n = f.fbq = function () {
        n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments);
      };
      if (!f._fbq) f._fbq = n;
      n.push = n;
      n.loaded = true;
      n.version = "2.0";
      n.queue = [];
      t = b.createElement(e);
      t.async = true;
      t.src = v;
      s = b.getElementsByTagName(e)[0];
      s.parentNode?.insertBefore(t, s);

      // Initialize pixel with ID
      window.fbq("init", pixelId);
      // window.fbq("track", "PageView");
    }
  }, [pixelId]);

  useEffect(() => {
    // Track page views on route changes
    if (window.fbq) {
      window.fbq("track", "PageView");
      window.fbq("track", "ViewContent");
    }
  }, [pathname]);

  return (
    <>
      <noscript>
        <Image
          height={1}
          width={1}
          style={{ display: "none" }}
          src={`https://www.facebook.com/tr?id=${pixelId}&ev=PageView&noscript=1`}
          alt="Facebook Pixel"
        />
      </noscript>
    </>
  );
};

export default FacebookPixel;
