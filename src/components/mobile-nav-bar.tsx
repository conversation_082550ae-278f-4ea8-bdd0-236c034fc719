"use client";

import React, { useEffect, useRef, useState } from "react";
import gsap from "gsap";
import Link from "next/link";
import Image from "next/image";
import { ChevronDown, X } from "lucide-react";
import { navigationItems, NavItem } from "@/config/navigation";

interface MobileNavBarProps {
  isOpen: boolean;
  onClose: () => void;
}

const MobileNavBar: React.FC<MobileNavBarProps> = ({ isOpen, onClose }) => {
  const [expandedMenus, setExpandedMenus] = useState<Set<string>>(new Set());
  const menuRef = useRef<HTMLDivElement>(null);
  const menuItemsRef = useRef<HTMLLIElement[]>([]);

  const toggleSubmenu = (menuName: string) => {
    const newExpanded = new Set<string>();
    if (!expandedMenus.has(menuName)) {
      newExpanded.add(menuName);
    }
    setExpandedMenus(newExpanded);
  };

  useEffect(() => {
    if (isOpen && menuItemsRef.current.length > 0) {
      // Set initial state for menu items
      gsap.set(menuItemsRef.current, {
        y: 120,
        autoAlpha: 0,
      });

      // GSAP animation for menu items
      const tl = gsap.timeline();
      tl.to(menuItemsRef.current, {
        duration: 0.8,
        y: 0,
        autoAlpha: 1,
        stagger: 0.15,
        ease: "power2.out",
        delay: 0.2, // Small delay to let the menu background appear first
      });
    } else if (!isOpen && menuItemsRef.current.length > 0) {
      // Reset animation when menu closes
      gsap.set(menuItemsRef.current, {
        y: 120,
        autoAlpha: 0,
      });
    }
  }, [isOpen]);

  return (
    <div className="block md:hidden">
      {/* Mobile Menu Overlay */}
      <div
        ref={menuRef}
        className={`fixed inset-0 z-[999999] transition-all duration-300 ease-out ${
          isOpen ? "visible opacity-100" : "invisible opacity-0"
        }`}
      >
        {/* Background */}
        <div className="absolute inset-0 bg-white shadow-2xl">
          {/* Logo */}
          <div className="absolute top-6 left-6 z-10">
            <Link href="/">
              <Image
                alt="epic"
                title="epic"
                width={100}
                height={100}
                src="/imgs/template/logo.svg?updatedAt=1747728314936&tr=w-100"
                className="h-auto w-30"
              />
            </Link>
          </div>

          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-6 right-6 z-10 flex h-12 w-12 items-center justify-center text-gray-600 transition-colors duration-200 hover:text-white"
          >
            <X className="h-8 w-8" />
          </button>

          {/* Menu Content */}
          <div
            className="h-full min-h-screen overflow-x-hidden overflow-y-auto bg-[#171819]"
            style={{
              backgroundImage: `
                linear-gradient(rgba(255, 255, 255, 0.04) 2px, transparent 2px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.04) 2px, transparent 2px),
                linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px)
              `,
              backgroundSize: "50px 50px, 50px 50px, 5px 5px, 5px 5px",
              backgroundPosition: "-2px -2px, -2px -2px, -1px -1px, -1px -1px",
            }}
          >
            <div className="px-8 pt-24 pb-8">
              <nav>
                <ul className="space-y-8">
                  {navigationItems.map((item, index) => (
                    <li
                      key={item.label}
                      ref={(el) => {
                        if (el) menuItemsRef.current[index] = el;
                      }}
                      className=""
                    >
                      <div className="flex items-center justify-between">
                        <div
                          className="hover:text-primary font-helvetica cursor-pointer text-4xl leading-tight font-medium text-gray-100 capitalize transition-colors duration-200 select-none"
                          style={{ fontSize: "3rem", lineHeight: "50px" }}
                          onClick={() => toggleSubmenu(item.label)}
                        >
                          {item.label}
                        </div>
                        {item.children && (
                          <button
                            onClick={() => toggleSubmenu(item.label)}
                            className="hover:text-primary ml-4 p-2 text-gray-400 transition-colors duration-200"
                          >
                            <ChevronDown
                              className={`h-6 w-6 transition-transform duration-200 ${
                                expandedMenus.has(item.label) ? "rotate-180" : ""
                              }`}
                            />
                          </button>
                        )}
                      </div>

                      {/* Submenu */}
                      {item.children && (
                        <div
                          className={`overflow-hidden transition-all duration-300 ease-out ${
                            expandedMenus.has(item.label)
                              ? "mt-4 max-h-96 opacity-100"
                              : "max-h-0 opacity-0"
                          }`}
                        >
                          <ul className="space-y-2 pl-4">
                            {item.children.map((subItem) => (
                              <li key={subItem.label}>
                                <a
                                  href={subItem.href}
                                  className="hover:text-primary block text-2xl font-normal text-white transition-all duration-200 hover:pl-3"
                                  style={{
                                    fontSize: "26px",
                                    lineHeight: "45px",
                                  }}
                                >
                                  {subItem.label}
                                </a>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileNavBar;
