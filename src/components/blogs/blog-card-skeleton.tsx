// Skeleton card component for loading state
const BlogCardSkeleton = () => (
  <div className="h-[500px] w-full max-w-[400px] flex-shrink-0 snap-start md:w-[400px] md:min-w-[350px]">
    <div className="relative h-full animate-pulse overflow-hidden rounded-[40px] border border-[#f5f0e8] bg-white">
      <div className="h-[200px] w-full rounded-t-[40px] bg-gray-300"></div>
      <div className="flex flex-col gap-4 px-5 pt-[10px] pb-[5px]">
        <div className="flex justify-end">
          <div className="h-6 w-32 rounded-2xl bg-gray-300"></div>
        </div>
        <div className="mb-3 h-6 w-full rounded bg-gray-300"></div>
        <div className="mb-2 h-4 w-full rounded bg-gray-300"></div>
        <div className="mb-4 h-4 w-3/4 rounded bg-gray-300"></div>
      </div>
      <div className="absolute right-[10px] bottom-[10px]">
        <div className="h-10 w-[141px] rounded-full bg-gray-300"></div>
      </div>
    </div>
  </div>
);

export default BlogCardSkeleton;
