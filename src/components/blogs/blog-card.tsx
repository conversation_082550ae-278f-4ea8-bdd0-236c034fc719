import Link from "next/link";
import React from "react";
import Image from "next/image";
import { formatDate } from "@/libs/utils";
import { ArrowRight } from "lucide-react";

interface BlogCardProps {
  blog: any;
}

const BlogCard = ({ blog }: BlogCardProps) => {
  return (
    <Link
      key={blog.id}
      href={`/blogs/${blog.slug}`}
      className="h-[500px] w-full max-w-[400px] flex-shrink-0 cursor-pointer snap-center md:w-[400px] md:min-w-[350px]"
    >
      <div className="font-neue-haas relative h-full overflow-hidden rounded-[40px] border border-[#f5f0e8] bg-white transition-shadow duration-300 hover:shadow-lg">
        <Image
          className="h-[200px] w-full rounded-t-[40px] object-cover object-center"
          src={blog.cover_image || blog.banner_image || "/imgs/placeholder.svg?"}
          width={400}
          height={200}
          alt={blog.cover_image_alt || blog.title}
          loading="lazy"
        />
        <div className="flex flex-col gap-4 px-5 pt-[10px] pb-[5px]">
          <div className="flex justify-end">
            <div className="inline-flex items-center justify-start rounded-2xl border border-[#fedf89] bg-[#fffaeb] px-3 py-[1px]">
              <p className="text-center text-[10px] font-normal break-words text-[#b54708]">
                {formatDate(blog.created_at)}
              </p>
            </div>
          </div>
          <h1 className="line-clamp-3 text-xl leading-none font-medium break-words text-black">
            {blog.title}
          </h1>
          <p className="line-clamp-7 text-sm leading-4 font-light break-words text-black">
            {blog.description}
          </p>
        </div>
        <button className="font-neue-haas absolute right-[10px] bottom-[10px] inline-flex min-w-[141px] items-center justify-start gap-[15px] overflow-hidden rounded-full border-transparent bg-[#1c5534] py-2 pr-2 pl-4">
          <span className="flex-1 text-center text-sm font-medium break-words text-white">
            Read more
          </span>
          <div className="relative flex h-6 w-6 items-center justify-center rounded-xl bg-white">
            <ArrowRight size={12} className="text-[#1c5534]" />
          </div>
        </button>
      </div>
    </Link>
  );
};

export default BlogCard;
