"use client";

import React, { useEffect } from "react";
import LoginDialog from "./login-dialog";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useUIStore } from "@/store/ui-store";
import { useAuthStore } from "@/store/auth-store";

const LoginButton = () => {
  const router = useRouter();
  const { openLoginDialog } = useUIStore();
  const { isAuthenticated, user, initializeAuth } = useAuthStore();

  // Initialize auth on component mount
  useEffect(() => {
    initializeAuth();
  }, [initializeAuth]);

  const handleClick = () => {
    if (isAuthenticated) {
      // Redirect to profile page
      router.push("/profile");
    } else {
      openLoginDialog();
    }
  };

  const profilePicture = user?.profile_image?.path;

  return (
    <>
      <button
        onClick={handleClick}
        className={`flex h-6 w-6 items-center justify-center rounded-full transition-transform duration-200 hover:scale-110 ${
          isAuthenticated ? "bg-white" : "bg-transparent"
        }`}
      >
        <Image
          src={profilePicture || "/imgs/profile.png"}
          alt="profile picture"
          title="profile picture"
          width={25}
          height={25}
          className="h-6 w-6 rounded-full object-cover"
          loading="lazy"
        />
      </button>

      <LoginDialog />
    </>
  );
};

export default LoginButton;
