import Image from "next/image";
import Link from "next/link";
import React from "react";
import { navigationItems } from "@/config/navigation";

const Footer = () => {
  return (
    <footer className="bg-primary text-white">
      {/* Main Footer Content */}
      <div className="px-4 py-12 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-12 lg:gap-12">
            {/* Logo and Social Media Section */}
            <div className="lg:col-span-4 xl:col-span-3">
              <div className="space-y-8">
                {/* Logo */}
                <div>
                  <Link href="/" className="inline-block">
                    <Image
                      src="/imgs/template/logo.svg"
                      alt="Epic Padel"
                      title="Epic Padel"
                      width={100}
                      height={100}
                      className="h-12 w-auto sm:h-16"
                      loading="lazy"
                    />
                  </Link>
                </div>

                {/* Social Media Links */}
                <div className="flex space-x-4">
                  <Link
                    href="https://www.instagram.com/epicpadel?igsh=eGFkenNiMWU1eWNo"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex h-10 w-10 items-center justify-center rounded-full bg-[#0c4825] transition-colors duration-200 hover:bg-gray-700"
                  >
                    <Image
                      src="/imgs/template/instagram.png"
                      alt="Follow us on Instagram"
                      title="Follow us on Instagram"
                      width={20}
                      height={20}
                      className="h-5 w-5"
                      loading="lazy"
                    />
                  </Link>
                  <Link
                    href="https://www.linkedin.com/company/epic-padel-inc/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex h-10 w-10 items-center justify-center rounded-full bg-[#0c4825] transition-colors duration-200 hover:bg-gray-700"
                  >
                    <Image
                      src="/imgs/template/linkedin.png"
                      alt="Connect with us on LinkedIn"
                      title="Connect with us on LinkedIn"
                      width={20}
                      height={20}
                      className="h-5 w-5"
                      loading="lazy"
                    />
                  </Link>
                  <Link
                    href="https://www.facebook.com/EpicPadelUSA?mibextid=ZbWKwL"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex h-10 w-10 items-center justify-center rounded-full bg-[#0c4825] transition-colors duration-200 hover:bg-gray-700"
                  >
                    <Image
                      src="/imgs/template/facebook.png"
                      alt="Like us on Facebook"
                      title="Like us on Facebook"
                      width={20}
                      height={20}
                      className="h-5 w-5"
                      loading="lazy"
                    />
                  </Link>
                </div>
              </div>
            </div>

            {/* Navigation Links */}
            <div className="lg:col-span-8 xl:col-span-9">
              <div className="grid grid-cols-2 gap-6 sm:grid-cols-3 lg:grid-cols-6 lg:gap-8">
                {navigationItems.map((section) => (
                  <div className="space-y-4" key={section.label}>
                    <h3 className="text-xl leading-relaxed font-medium text-white">
                      {section.label}
                    </h3>
                    <ul className="space-y-3">
                      {section.children?.map((item) => (
                        <li key={item.label}>
                          <Link
                            href={item.href || "#"}
                            className="text-sm text-gray-300 transition-colors duration-200 hover:text-white"
                          >
                            {item.label}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>

            {/* App Store Links */}
            <div className="mt-8 lg:col-span-12 lg:mt-12">
              <div className="flex flex-col items-start gap-4 sm:flex-row sm:items-center sm:gap-6">
                <Link
                  href="https://apps.apple.com/ae/app/epic-padel/id6501984934"
                  className="transition-transform duration-300 hover:scale-110"
                  title="Download on the App Store"
                >
                  <Image
                    className="h-12 w-auto object-cover sm:h-14"
                    src="/imgs/template/icons/app-store.png"
                    alt="Download on the App Store"
                    title="Download on the App Store"
                    width={192}
                    height={48}
                    loading="lazy"
                  />
                </Link>
                <Link
                  href="https://play.google.com/store/apps/details?id=com.epicpadel.mobile"
                  className="transition-transform duration-300 hover:scale-110"
                  title="Get it on Google Play"
                >
                  <Image
                    className="h-12 w-auto sm:h-14"
                    src="/imgs/template/icons/google-play.png"
                    alt="Get it on Google Play"
                    title="Get it on Google Play"
                    width={192}
                    height={48}
                    loading="lazy"
                  />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-[#0c4825] bg-[#0c4825]">
        <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
          <div className="flex flex-col items-center justify-between space-y-4 sm:flex-row sm:space-y-0">
            <div className="text-sm text-gray-400">
              Copyright © 2025 EPIC Inc. All rights reserved.
            </div>
            <div className="flex flex-col space-y-2 text-sm sm:flex-row sm:space-y-0 sm:space-x-6">
              <a
                href="/terms-and-conditions"
                className="text-gray-400 transition-colors duration-200 hover:text-white"
              >
                Terms and Conditions & Privacy Policy
              </a>
              <a
                href="/terms-of-use"
                className="text-gray-400 transition-colors duration-200 hover:text-white"
              >
                Terms of Use
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
