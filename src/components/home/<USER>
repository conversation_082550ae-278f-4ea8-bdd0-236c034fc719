"use client";

import React, { useEffect, useRef } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Image from "next/image";
import Link from "next/link";

// Register GSAP plugins
if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

const DownloadAppSection = () => {
  const appImageRef = useRef<HTMLImageElement>(null);
  const imageContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      if (appImageRef.current && imageContainerRef.current) {
        // Initial state - slightly scaled down and transparent
        gsap.set(appImageRef.current, {
          scale: 0.8,
          opacity: 0,
          y: 100,
        });

        // Entrance animation when element comes into view
        gsap.to(appImageRef.current, {
          scale: 1,
          opacity: 1,
          y: 0,
          duration: 1.5,
          ease: "power3.out",
          scrollTrigger: {
            trigger: imageContainerRef.current,
            start: "top 80%",
            toggleActions: "play none none reverse",
          },
        });

        // Parallax scroll effect
        gsap.to(appImageRef.current, {
          y: -50,
          duration: 1,
          ease: "none",
          scrollTrigger: {
            trigger: imageContainerRef.current,
            start: "top bottom",
            end: "bottom top",
            scrub: 1,
          },
        });

        // Subtle rotation on scroll
        gsap.to(appImageRef.current, {
          rotation: 5,
          duration: 1,
          ease: "none",
          scrollTrigger: {
            trigger: imageContainerRef.current,
            start: "top 60%",
            end: "bottom 40%",
            scrub: 2,
          },
        });

        // Scale effect on scroll
        gsap.to(appImageRef.current, {
          scale: 1.1,
          duration: 1,
          ease: "none",
          scrollTrigger: {
            trigger: imageContainerRef.current,
            start: "top 50%",
            end: "bottom 30%",
            scrub: 1,
          },
        });
      }
    });

    return () => ctx.revert();
  }, []);

  return (
    <section className="bg-0 flex w-full justify-center px-4 pt-4 pb-15" id="6">
      <div
        className="bg-primary relative container flex h-[400px] w-full flex-row justify-between rounded-[3rem] bg-[url('/imgs/template/icons/Layer_1.png')] p-8 md:h-[500px] lg:p-24"
        style={{
          backgroundPosition: "center",
          backgroundSize: "contain !important",
          backgroundRepeat: "no-repeat",
        }}
      >
        <div className="flex max-w-1/2 flex-col items-start justify-center gap-4">
          <h4 className="bg-secondary text-primary inline-flex w-fit rounded-full px-6 py-2 font-bold whitespace-nowrap">
            Available Now
          </h4>
          <h3 className="font-medium text-white">Download the app</h3>
          <p className="text-white">and schedule your court time conveniently!</p>
          <div className="flow-col flex gap-4 md:flex-row">
            <Link
              href="https://apps.apple.com/ae/app/epic-padel/id6501984934"
              title="app store link"
            >
              <Image
                className="h-auto w-40"
                src="/imgs/template/icons/app-store.png"
                alt="app store"
                title="app store"
                width={192}
                height={48}
                loading="lazy"
              />
            </Link>
            <Link
              href="https://play.google.com/store/apps/details?id=com.epicpadel.mobile"
              title="play store link"
            >
              <Image
                className="h-auto w-40"
                src="/imgs/template/icons/google-play.png"
                alt="play store"
                title="play store"
                width={192}
                height={48}
                loading="lazy"
              />
            </Link>
          </div>
        </div>
        <div
          ref={imageContainerRef}
          className="absolute hidden w-40 md:w-96 lg:top-[-102px] lg:right-20 lg:block"
        >
          <div className="">
            <Image
              width={400}
              height={400}
              ref={appImageRef}
              className="parallax-image"
              src="/imgs/page/portfolio/app.png"
              sizes="(max-width: 768px) 100vw, 50vw"
              alt="epic padel app"
              title="epic padel app"
              loading="lazy"
              decoding="async"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default DownloadAppSection;
