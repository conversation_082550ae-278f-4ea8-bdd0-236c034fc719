"use client";

import { endpoints } from "@/api/axios";
import MembershipDialog from "@/components/dialogs/membership-dialog";
import { Plan, PlansResponse, User } from "@/types/membership";
import { useEffect, useState } from "react";
import { PlanCards } from "../membership/plan-cards";
import { SkeletonCards } from "../membership/skeleton-components";

const MembershipSection = () => {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [isYearly, setIsYearly] = useState(true);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);

  // Fetch plans from API
  const fetchPlans = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL!}${endpoints.public.packages}`
      );
      const data: PlansResponse = await response.json();
      setPlans(data.data);
    } catch (error) {
      console.error("Error fetching plans:", error);
    } finally {
      setLoading(false);
    }
  };

  // Get user data from localStorage
  useEffect(() => {
    const userString = localStorage.getItem("user");
    if (userString) {
      try {
        const userData = JSON.parse(userString);
        setUser(userData);
      } catch (error) {
        console.error("Error parsing user data:", error);
      }
    }
  }, []);

  // Fetch plans on component mount and when billing type changes
  useEffect(() => {
    fetchPlans();
  }, [isYearly]);

  const handleBillingToggle = (yearly: boolean) => {
    setIsYearly(yearly);
  };

  return (
    <section id="">
      <div
        className="flex flex-col items-center justify-center overflow-hidden px-4 pt-4 font-bold"
        id="membership"
      >
        <div className="container px-4">
          <div className="mb-10 text-center">
            <h2 className="text-primary mb-2 text-4xl font-bold tracking-tight lg:text-6xl">
              Membership
            </h2>
            <h3 className="heading-5 color-900 text-lg md:text-xl">
              Become a Founding Member and join the Epic Padel Family
            </h3>
          </div>
        </div>
      </div>

      {/* Locations toggle button section */}
      <div className="font-helvetica mb-8 flex justify-center px-4 font-bold">
        <div className="relative flex w-full max-w-[480px] gap-2 rounded-full bg-gray-100 px-1 py-1 lg:gap-4">
          <div className="bg-primary cursor-pointer rounded-full px-4 py-2 text-center text-sm font-bold text-white shadow-md md:text-base lg:py-3">
            Charlotte, NC
          </div>
          <div className="relative flex cursor-not-allowed flex-col items-center justify-center text-sm opacity-50 md:text-base">
            <p className="text-center">Salt Lake City, UT</p>
            <p className="text-xs whitespace-nowrap text-gray-500">Coming Soon</p>
          </div>
          <div className="relative flex cursor-not-allowed flex-col items-center justify-center text-sm opacity-50 md:text-base">
            <span className="text-center">Tyson's Corner, VA</span>
            <span className="text-xs whitespace-nowrap text-gray-500">Coming Soon</span>
          </div>
        </div>
      </div>

      {/* Billing Toggle */}
      <div className="mb-10 flex justify-center">
        <div className="relative w-full max-w-84 rounded-full bg-gray-100 px-4 py-1">
          <div
            className="bg-primary absolute top-1 bottom-1 w-1/2 rounded-full font-medium transition-all duration-300 ease-out"
            style={{ left: isYearly ? "2px" : "50%" }}
          ></div>
          <button
            aria-label="Monthly Billing"
            className={`font-helvetica relative z-10 w-1/2 rounded-full px-3 py-2 text-sm font-bold transition-colors duration-300 ${
              isYearly ? "text-white" : "text-gray-600 hover:text-gray-800"
            }`}
            onClick={() => handleBillingToggle(true)}
          >
            Monthly Billing
          </button>
          <button
            className={`font-helvetica relative z-10 w-1/2 rounded-full px-3 py-2 text-sm font-bold transition-colors duration-300 ${
              !isYearly ? "text-white" : "text-gray-600 hover:text-gray-800"
            }`}
            onClick={() => handleBillingToggle(false)}
          >
            Annual Billing
          </button>
        </div>
      </div>

      {/* Plan Cards Container */}
      <div className="mt-10 mb-16 flex flex-wrap items-stretch justify-center gap-5 px-4">
        {loading ? <SkeletonCards /> : <PlanCards plans={plans} isYearly={isYearly} user={user} />}
      </div>

      {/* Membership Dialog */}
      <MembershipDialog />
    </section>
  );
};

export default MembershipSection;
