import React from "react";
import Image from "next/image";
import PrimaryLinkButton from "../primary-link-button";
import Link from "next/link";

const locations = [
  {
    id: "charlotte",
    name: "Charlotte, NC",
    description: `<p><strong>Epic Padel is opening soon at Prosperity Athletic Club!</strong></p>
We're opening 5 outdoor padel courts this summer in Charlotte!
Memberships now on sale - enjoy unlimited bookings, exclusive perks & early access.`,
    href: "/locations/charlotte",
  },
  {
    id: "utah",
    name: "Salt Lake City, UT",
    description: `<p><strong>Epic Padel is coming to Utah!</strong></p>
Our outdoor facility featuring padel and other racket sports opens at the end of this summer!
Get ready to play, connect, and compete.
More updates coming soon!`,
    href: "/locations/utah",
  },
  {
    id: "virginia",
    name: "Tyson's Corner, VA",
    description: `We're excited to announce that Epic Padel is coming to Tysons Corner, Virginia in Spring 2026!
This all-padel club will bring the energy and excitement of the sport to the heart of Tysons.
Whether you're new to padel or a seasoned player, get ready to be part of something epic.
More updates coming soon!
`,
    href: "/locations/virginia",
  },
];

const LocationSection = () => {
  return (
    <div className="my-4 flex w-full flex-col items-center md:my-12">
      <section
        className="container flex flex-col items-center overflow-hidden pt-12"
        id="locations"
      >
        <div className="container mx-auto px-4">
          <div className="mb-10 text-center">
            <h2 className="text-primary mb-2 text-4xl font-bold tracking-tight lg:text-6xl">
              Our locations
            </h2>
            <h3 className="heading-5 color-900 text-lg md:text-xl">Discover our padel clubs</h3>
          </div>
        </div>
      </section>

      <section className="container mx-auto px-4 pb-20">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {locations.map((location) => (
            <div
              key={location.id}
              className="transform overflow-hidden rounded-2xl bg-transparent transition"
            >
              <div className="flex flex-col p-6">
                <div className="mb-6 flex justify-center">
                  <div className="relative h-20 w-16">
                    <Image
                      src="/imgs/template/padel.svg"
                      alt="Padel icon"
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      style={{ objectFit: "contain" }}
                      loading="lazy"
                    />
                  </div>
                </div>

                <h3 className="text-primary mb-4 text-center text-2xl font-bold">
                  {location.href ? (
                    <Link href={location.href} className="text-primary hover:underline">
                      {location.name}
                    </Link>
                  ) : (
                    location.name
                  )}
                </h3>

                <p
                  className="mb-4 flex-grow text-center text-base font-medium text-gray-700 lg:min-h-[180px]"
                  dangerouslySetInnerHTML={{ __html: location.description }}
                ></p>

                <PrimaryLinkButton href={location.href} text="View" className="mx-auto py-2" />
              </div>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
};

export default LocationSection;
