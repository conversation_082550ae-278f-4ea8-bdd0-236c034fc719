"use client";

import React, { useRef, useEffect, useState } from "react";
import { Star, ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";

// TypeScript interface for testimonial data
interface Testimonial {
  text: string;
  author: string;
  title: string;
  rating: number;
  linkedIn: string;
}

const TestimonialSection = () => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoScrolling, setIsAutoScrolling] = useState(true);

  // Testimonial data array
  const testimonials: Testimonial[] = [
    {
      text: "Epic Padel isn't just building clubs—they're building an ecosystem. Their vision aligns perfectly with where the racquet sports industry is heading.",
      author: "<PERSON>",
      title: "COO at Blue Chip Sports Management",
      rating: 5,
      linkedIn: "",
    },
    {
      text: "What Epic Padel is doing across North America reminds me of the early days of boutique fitness—nimble, premium, and community-first.",
      author: "<PERSON>",
      title: "Director of Racquets at Dallas Country Club",
      rating: 5,
      linkedIn: "https://www.linkedin.com/in/michael-jordan-dca-022bb921/",
    },
    {
      text: "Epic Padel has cracked the code—world-class courts, technology-driven systems, and a scalable model rooted in retention.",
      author: "Kyle LaCroix",
      title: "CEO of Sets Consulting",
      rating: 5,
      linkedIn: "https://www.linkedin.com/in/kyle-lacroix-8b512b1b/",
    },
    {
      text: "They're not just adding padel to the mix—they're redefining what a modern racquet club can be.",
      author: "Chris Gale",
      title: "Directors of Racquets at Bent Tree Country Club",
      rating: 5,
      linkedIn: "https://www.linkedin.com/in/chris-gale-dca-61667170/",
    },
    {
      text: "Epic Padel's ability to merge innovation with traditional racquet sports values is exactly what our industry needs.",
      author: "Boris Fetbroyt",
      title: "Director of Racquets at Philadelphia Cricket Club",
      rating: 5,
      linkedIn: "https://www.linkedin.com/in/boris-fetbroyt-rspa-crse-dca-38380792/",
    },
    {
      text: "Epic Padel's growth is no accident—they're putting in the work, building relationships, and delivering value at every touchpoint.",
      author: "Jarrett Chirico",
      title: "Director of Racquets at Royal Oaks Country Club",
      rating: 5,
      linkedIn: "https://www.linkedin.com/in/jarrett-chirico-dca-b7712649/",
    },
    {
      text: "From talent acquisition to court activation, Epic Padel sets a new benchmark for excellence in racquet sports infrastructure.",
      author: "Jeremey Speicher",
      title: "Owner of Prosperity Athletic Club",
      rating: 5,
      linkedIn: "https://www.linkedin.com/in/jeremy-speicher-rspa-a9282210/",
    },
  ];

  // Auto-scroll functionality
  useEffect(() => {
    if (!isAutoScrolling) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % testimonials.length;
        scrollToIndex(nextIndex);
        return nextIndex;
      });
    }, 10000);

    return () => clearInterval(interval);
  }, [isAutoScrolling, testimonials.length]);

  // Scroll to specific index
  const scrollToIndex = (index: number) => {
    if (scrollContainerRef.current) {
      const cardWidth = 320; // Card width + gap
      const scrollPosition = index * cardWidth;
      scrollContainerRef.current.scrollTo({
        left: scrollPosition,
        behavior: "smooth",
      });
    }
  };

  // Navigation functions
  const scrollLeft = () => {
    setIsAutoScrolling(false);
    const newIndex = currentIndex > 0 ? currentIndex - 1 : testimonials.length - 1;
    setCurrentIndex(newIndex);
    scrollToIndex(newIndex);

    // Resume auto-scroll after 10 seconds
    setTimeout(() => setIsAutoScrolling(true), 10000);
  };

  const scrollRight = () => {
    setIsAutoScrolling(false);
    const newIndex = (currentIndex + 1) % testimonials.length;
    setCurrentIndex(newIndex);
    scrollToIndex(newIndex);

    // Resume auto-scroll after 10 seconds
    setTimeout(() => setIsAutoScrolling(true), 10000);
  };

  return (
    <section className="section scrollbar-hide w-full" id="testimonials">
      <div className="mt-16 flex h-auto min-h-[600px] w-full flex-col items-center justify-start bg-[#1C5534] px-4 py-16">
        <h1 className="mb-2 text-4xl font-bold tracking-tight text-[#FFFAED] lg:text-6xl">
          Testimonials
        </h1>
        <p className="mb-6 text-lg text-[#D6CAAB] md:text-xl">How We&apos;re Making a Difference</p>

        {/* Horizontal Scrollable Container */}
        <div className="relative mx-auto w-full max-w-[400px] md:max-w-none">
          <div
            ref={scrollContainerRef}
            className="scrollbar-hide flex snap-x snap-center gap-5 overflow-x-auto scroll-smooth px-4 md:gap-8"
            style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
            onMouseEnter={() => setIsAutoScrolling(false)}
            onMouseLeave={() => setIsAutoScrolling(true)}
          >
            {testimonials.map((testimonial, index) => (
              <div key={index} className="flex-shrink-0">
                <TestimonialCard testimonial={testimonial} />
              </div>
            ))}
          </div>
        </div>

        {/* Navigation Arrows */}
        <div className="mt-6 flex items-center justify-center gap-4">
          <button
            onClick={scrollLeft}
            className="rounded-full bg-white/10 p-2 backdrop-blur-sm transition-colors duration-200 hover:bg-white/20"
            aria-label="Previous testimonial"
          >
            <ChevronLeft className="h-6 w-6 text-white" />
          </button>

          <div className="flex gap-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  setIsAutoScrolling(false);
                  setCurrentIndex(index);
                  scrollToIndex(index);
                  setTimeout(() => setIsAutoScrolling(true), 10000);
                }}
                className={`h-2 w-2 rounded-full transition-colors duration-200 ${
                  index === currentIndex ? "bg-white" : "bg-white/40"
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>

          <button
            onClick={scrollRight}
            className="rounded-full bg-white/10 p-2 backdrop-blur-sm transition-colors duration-200 hover:bg-white/20"
            aria-label="Next testimonial"
          >
            <ChevronRight className="h-6 w-6 text-white" />
          </button>
        </div>
      </div>
    </section>
  );
};

// Individual Testimonial Card Component
interface TestimonialCardProps {
  testimonial: Testimonial;
}

const TestimonialCard: React.FC<TestimonialCardProps> = ({ testimonial }) => {
  return (
    <div className="testimonial-card relative flex h-[260px] w-[300px] flex-col items-center rounded-3xl bg-white/10 p-4 text-center backdrop-blur-sm md:w-[400px] md:p-6">
      <div className="mb-4 flex gap-1">
        {Array.from({ length: testimonial.rating }, (_, i) => (
          <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400 md:h-5 md:w-5" />
        ))}
      </div>

      <p className="mb-6 flex max-h-[200px] items-center justify-center px-2 text-center text-xs leading-6 font-normal tracking-wide text-white md:text-sm">
        &quot;{testimonial.text}&quot;
      </p>

      <div className="absolute bottom-4 flex flex-col items-center gap-2">
        <div className="text-center">
          <p className="mb-1 text-xs font-bold tracking-wide text-white md:text-sm">
            {testimonial.author}
          </p>
          <p className="max-w-2xs text-xs font-normal tracking-wide text-[#D6CAAB] md:text-sm">
            {testimonial.title}
          </p>
        </div>
        {testimonial.linkedIn && (
          <a
            href={testimonial.linkedIn}
            target="_blank"
            rel="noopener noreferrer"
            className="p-0.5"
          >
            <Image
              src="/imgs/template/linkedin.png"
              alt="LinkedIn"
              title="Connect with us on LinkedIn"
              className="h-4 w-4 md:h-5 md:w-5"
              width={20}
              height={20}
              loading="lazy"
            />
          </a>
        )}
      </div>
    </div>
  );
};

export default TestimonialSection;
