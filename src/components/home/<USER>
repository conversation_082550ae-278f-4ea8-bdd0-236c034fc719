"use client";

import { endpoints } from "@/api/axios";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import BlogCard from "../blogs/blog-card";
import BlogCardSkeleton from "../blogs/blog-card-skeleton";

// TypeScript interfaces for the blog data structure
interface BlogItem {
  id: number;
  slug: string;
  title: string;
  description: string;
  content?: string;
  cover_image?: string;
  banner_image?: string;
  cover_image_alt?: string;
  created_at: string;
  meta_keywords?: string;
}

interface BlogApiResponse {
  data: {
    data: BlogItem[];
  };
}

const BlogsSection = () => {
  const [blogs, setBlogs] = useState<BlogItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  // Fetch blogs from API
  const fetchBlogs = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL!}${endpoints.public.blog.list}`,
        { cache: "no-store" }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch blogs");
      }

      const data: BlogApiResponse = await response.json();

      if (data && data.data && data.data.data) {
        setBlogs(data.data.data);
      } else {
        setBlogs([]);
      }
    } catch (error) {
      console.error("Error fetching blog data:", error);
      setError("Failed to load blogs");
      setBlogs([]);
    } finally {
      setLoading(false);
    }
  };

  // Check scroll position and update button states
  const updateScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);

      // Update current index based on scroll position
      // Mobile: full width card, Desktop: 420px (400px + 20px gap)
      const isMobile = window.innerWidth < 768;
      const cardWidth = isMobile ? clientWidth : 420;
      const newIndex = Math.round(scrollLeft / cardWidth);
      setCurrentIndex(newIndex);
    }
  };

  // Scroll to specific index
  const scrollToIndex = (index: number) => {
    if (scrollContainerRef.current) {
      // Mobile: full width card, Desktop: 420px (400px + 20px gap)
      const isMobile = window.innerWidth < 768;
      const cardWidth = isMobile ? scrollContainerRef.current.clientWidth : 420;
      scrollContainerRef.current.scrollTo({
        left: index * cardWidth,
        behavior: "smooth",
      });
    }
  };

  // Scroll left function
  const scrollLeft = () => {
    const newIndex = Math.max(0, currentIndex - 1);
    setCurrentIndex(newIndex);
    scrollToIndex(newIndex);
  };

  // Scroll right function
  const scrollRight = () => {
    const maxIndex = Math.max(0, blogs.length - 1);
    const newIndex = Math.min(maxIndex, currentIndex + 1);
    setCurrentIndex(newIndex);
    scrollToIndex(newIndex);
  };

  useEffect(() => {
    fetchBlogs();
  }, []);

  // Update scroll buttons when blogs change
  useEffect(() => {
    if (!loading && blogs.length > 0) {
      setTimeout(updateScrollButtons, 100);
    }
  }, [blogs, loading]);

  // Handle window resize for responsive calculations
  useEffect(() => {
    const handleResize = () => {
      updateScrollButtons();
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <section className="w-full overflow-hidden py-16">
      {/* Header Section */}
      <div
        className="flex w-full flex-col items-center overflow-hidden pt-8 md:pt-12"
        id="locations"
      >
        <div className="container mx-auto px-4">
          <div className="mb-6 text-center md:mb-10">
            <h2 className="text-primary mb-2 text-3xl font-bold tracking-tight lg:text-6xl">
              Blogs
            </h2>
            <h3 className="heading-5 color-900 text-base md:text-xl">
              Insights, tips, and stories from the world of padel
            </h3>
          </div>
        </div>
      </div>

      {/* Blogs Container */}
      <section className="w-full">
        <div className="w-full px-0 md:px-4">
          <div className="py-8">
            <div
              ref={scrollContainerRef}
              className="scrollbar-hide snap-x snap-mandatory overflow-x-auto scroll-smooth"
              onScroll={updateScrollButtons}
            >
              <div
                className={`flex justify-start gap-2 px-2 pb-4 md:gap-6 md:px-6 2xl:justify-start`}
              >
                {loading ? (
                  // Loading skeleton cards
                  <>
                    <BlogCardSkeleton />
                    <BlogCardSkeleton />
                    <BlogCardSkeleton />
                    <BlogCardSkeleton />
                    <BlogCardSkeleton />
                    <BlogCardSkeleton />
                    <BlogCardSkeleton />
                  </>
                ) : error ? (
                  // Error state
                  <div className="w-full py-12 text-center">
                    <p className="text-lg text-gray-600">{error}</p>
                  </div>
                ) : blogs.length > 0 ? (
                  blogs.map((blog) => <BlogCard key={blog.id} blog={blog} />)
                ) : (
                  // No blogs state
                  <div className="w-full py-12 text-center">
                    <p className="text-lg text-gray-600">No blogs available at this time.</p>
                  </div>
                )}
              </div>
            </div>

            {/* Navigation Controls - Testimonial Style */}
            {!loading && !error && blogs.length > 0 && (
              <div className="mt-6 flex items-center justify-center gap-4">
                <button
                  onClick={scrollLeft}
                  disabled={!canScrollLeft}
                  className={`rounded-full p-2 backdrop-blur-sm transition-colors duration-200 ${
                    canScrollLeft
                      ? "bg-primary/10 hover:bg-primary/20 text-primary"
                      : "cursor-not-allowed bg-gray-200 text-gray-400"
                  }`}
                  aria-label="Previous blog"
                >
                  <ChevronLeft className="h-6 w-6" />
                </button>

                <div className="flex gap-2">
                  {blogs.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        setCurrentIndex(index);
                        scrollToIndex(index);
                      }}
                      className={`h-2 w-2 rounded-full transition-colors duration-200 ${
                        index === currentIndex ? "bg-primary" : "bg-primary/40"
                      }`}
                      aria-label={`Go to blog ${index + 1}`}
                    />
                  ))}
                </div>

                <button
                  onClick={scrollRight}
                  disabled={!canScrollRight}
                  className={`rounded-full p-2 backdrop-blur-sm transition-colors duration-200 ${
                    canScrollRight
                      ? "bg-primary/10 hover:bg-primary/20 text-primary"
                      : "cursor-not-allowed bg-gray-200 text-gray-400"
                  }`}
                  aria-label="Next blog"
                >
                  <ChevronRight className="h-6 w-6" />
                </button>
              </div>
            )}
          </div>
        </div>
      </section>
    </section>
  );
};

export default BlogsSection;
