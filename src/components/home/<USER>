"use client";

import { useEffect, useRef, useState } from "react";
import PrimaryLinkButton from "../primary-link-button";
import Image from "next/image";
import gsap from "gsap";
import SplitText from "gsap/SplitText";
import Link from "next/link";

const HeaderSection = () => {
  const [isMobile, setIsMobile] = useState(false);
  const titleRef = useRef<HTMLHeadingElement>(null);

  useEffect(() => {
    // Initial check
    setIsMobile(window.innerWidth < 768);

    // Add resize listener
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);

    // GSAP Animation
    if (titleRef.current) {
      const splitText = new SplitText(titleRef.current, { type: "chars" });
      const chars = splitText.chars;

      gsap.set(chars, { opacity: 0 });

      const tl = gsap.timeline();
      tl.to(chars, {
        opacity: 1,
        duration: 0.1,
        stagger: 0.05,
        ease: "none",
      });
    }

    return () => {
      window.removeEventListener("resize", handleResize);
      // Cleanup GSAP animations
      if (titleRef.current) {
        gsap.killTweensOf(titleRef.current);
      }
    };
  }, []);

  return (
    <section className="bg-background relative h-screen w-full overflow-hidden" id="hero-section">
      {/* Video Background */}
      <div className="absolute inset-0 z-0">
        {isMobile ? (
          <video
            className="absolute inset-0 h-full w-full object-cover"
            autoPlay
            muted
            loop
            playsInline
            preload="auto"
            poster="/imgs/epic_poster_v.jpg"
          >
            <source src="/videos/epic_padel_v.mp4" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        ) : (
          <video
            className="absolute inset-0 h-full w-full object-cover"
            autoPlay
            muted
            loop
            playsInline
            preload="auto"
            poster="/imgs/epic_poster_h.jpg"
          >
            <source src="/videos/epic_padel_h.mp4" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        )}

        {/* Video Overlay */}
        <div className="absolute inset-0 z-10 bg-black/20"></div>
      </div>

      {/* Content */}
      <div className="relative z-20 flex h-full flex-col items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="mx-auto flex max-w-4xl flex-col items-center text-center 2xl:space-y-4">
          {/* Main Heading */}
          <h1
            ref={titleRef}
            className="max-w-[320px] text-5xl leading-tight font-bold tracking-tight text-white sm:text-5xl md:text-6xl lg:max-w-full lg:text-7xl 2xl:text-8xl"
          >
            The lines have been drawn
          </h1>

          {/* CTA Button */}
          <div className="mt-6">
            <PrimaryLinkButton href="/contact" text="Book a Court" />
          </div>

          {/* App Store Links */}
          <div className="mt-6 flex flex-col items-center justify-center gap-4 sm:mt-12 sm:flex-row sm:gap-6">
            <Link
              href="https://apps.apple.com/ae/app/epic-padel/id6501984934"
              className="transition-transform duration-300 hover:scale-110"
              title="Download on the App Store"
            >
              <Image
                className="h-12 w-auto sm:h-14 md:h-16"
                src="/imgs/template/icons/app-store.png"
                alt="Download on the App Store"
                title="Download on the App Store"
                width={192}
                height={48}
                loading="lazy"
              />
            </Link>
            <Link
              href="https://play.google.com/store/apps/details?id=com.epicpadel.mobile"
              className="transition-transform duration-300 hover:scale-110"
              title="Get it on Google Play"
            >
              <Image
                className="h-12 w-auto sm:h-14 md:h-16"
                src="/imgs/template/icons/google-play.png"
                alt="Get it on Google Play"
                title="Get it on Google Play"
                width={192}
                height={48}
                loading="lazy"
              />
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeaderSection;
