"use client";

import React, { useState, useEffect, useRef } from "react";
import { ChevronDown } from "lucide-react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import PrimaryLinkButton from "../primary-link-button";

// Register GSAP plugins
if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  expanded?: boolean;
}

const faqItems: FAQItem[] = [
  {
    id: "One",
    question: "What is padel tennis?",
    answer:
      "Padel tennis is a racquet sport that combines elements of tennis, squash, and racquetball. It is typically played in doubles on an enclosed court about a third the size of a tennis court. A padel court is 20m long by 10m wide, and is made up of glass and wired mesh for the rebound walls.",
    expanded: true,
  },
  {
    id: "Two",
    question: "How is padel tennis different from regular tennis?",
    answer:
      "Padel tennis is played on a smaller court with walls and the balls can be played off them, similar to squash. The racquets are solid and stringless, which are different from the stringed racquets used in tennis. The scoring system is the same as tennis and the balls used are similar to tennis balls but have a little less pressure.",
  },
  {
    id: "Three",
    question: "What equipment do I need to play padel tennis?",
    answer:
      "You will need a padel racquet, which is typically shorter and has a solid surface with no strings. Padel balls, which are similar to tennis balls but slightly softer, are also necessary. Comfortable sports attire and tennis shoes are recommended.",
  },
  {
    id: "Four",
    question: "Is padel tennis easy to learn?",
    answer:
      "Yes, padel tennis is generally easier to learn than tennis, making it accessible to players of all ages and skill levels. The smaller court and the use of walls reduce the amount of movement required, and the scoring and basic rules are simple to grasp.",
  },
  {
    id: "Five",
    question: "Can padel tennis be played in all weather conditions?",
    answer:
      "Padel courts are often outdoors, but they can also be covered or indoors, allowing for play in various weather conditions. Outdoor courts may be subject to weather limitations similar to other outdoor sports.",
  },
  {
    id: "Six",
    question: "Is padel tennis a good workout?",
    answer:
      "Absolutely! Padel tennis provides a comprehensive cardiovascular workout, combining elements of agility, speed, and coordination. The dynamic nature of the game means that it's also great for improving reaction times and muscle tone. Lastly, its low-impact nature distinguishes it from other racket sports, appealing to those seeking a gentler yet effective workout program.",
  },
  {
    id: "Seven",
    question: "How long does a game of padel tennis last?",
    answer:
      "The duration of a padel match can vary, but it generally lasts about one to one and a half hours, similar to a tennis match. It depends on the players' levels and the match format.",
  },
  {
    id: "Eight",
    question: "What are the rules?",
    answer: `
      <ul class="list-disc list-inside space-y-2">
        <li>The ball must be served underhand and must bounce on the server's side of the court before bouncing on the receiver's side of the court.</li>
        <li>The ball can be hit off the walls of the court as part of the game, and players can use the walls to create a variety of angles and shots.</li>
        <li>A point is awarded when the ball hits the floor on the opponent's side of the court, or when the opponent commits a fault (such as hitting the ball out of bounds or into the net).</li>
        <li>You can run out of the court to play the ball back onto your opponent's side.</li>
        <li>Padel tennis uses the same scoring system as tennis, with games scored as "love," "15," "30," "40," and "game."</li>
        <li>The first team to win six games wins the set, and the first team to win two sets wins the match.</li>
      </ul>
    `,
  },
  {
    id: "Nine",
    question: "Is padel tennis a competitive sport?",
    answer:
      "Yes, padel tennis can be highly competitive. There are tournaments and leagues at various levels, from local club competitions to international championships.",
  },
];

const FaqSection = () => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(
    new Set(faqItems.filter((item) => item.expanded).map((item) => item.id))
  );
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const accordionRef = useRef<HTMLDivElement>(null);
  const contactBoxRef = useRef<HTMLDivElement>(null);

  const toggleItem = (id: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  useEffect(() => {
    if (typeof window === "undefined") return;

    const ctx = gsap.context(() => {
      // Title animation
      if (titleRef.current) {
        gsap.fromTo(
          titleRef.current,
          {
            opacity: 0,
            y: 50,
          },
          {
            opacity: 1,
            y: 0,
            duration: 1,
            ease: "power3.out",
            scrollTrigger: {
              trigger: titleRef.current,
              start: "top 80%",
              toggleActions: "play none none reverse",
            },
          }
        );
      }

      // Contact box grow-up animation
      if (contactBoxRef.current) {
        gsap.fromTo(
          contactBoxRef.current,
          {
            autoAlpha: 0.5,
            scale: 0.7,
          },
          {
            autoAlpha: 1,
            scale: 1,
            duration: 2,
            scrollTrigger: {
              trigger: contactBoxRef.current,
              start: "top 90%",
            },
          }
        );
      }

      // Accordion items scroll-move-up-2 animation
      const accordionItems = accordionRef.current?.querySelectorAll(".accordion-item");
      if (accordionItems) {
        accordionItems.forEach((item, index) => {
          gsap.fromTo(
            item,
            {
              opacity: 0,
              y: 100,
            },
            {
              opacity: 1,
              y: 0,
              duration: 0.8,
              delay: index * 0.1,
              ease: "power3.out",
              scrollTrigger: {
                trigger: item,
                start: "top 85%",
                toggleActions: "play none none reverse",
              },
            }
          );
        });
      }
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  return (
    <section ref={sectionRef} className="bg-background relative py-20" id="faq-section">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div ref={titleRef} className="mb-16 text-center">
          <h2 className="text-primary font-neue-haas mb-4 text-4xl font-bold md:text-5xl lg:text-6xl">
            FAQs
          </h2>
          <h6 className="text-lg font-medium text-gray-700 md:text-xl">
            The most frequently asked padel questions.
          </h6>
        </div>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-12 lg:gap-12">
          {/* Contact Box */}
          <div className="lg:col-span-3">
            <div ref={contactBoxRef} className="lg:mt-20">
              <div className="mb-8">
                <h6 className="mb-4 text-lg font-semibold text-gray-900">
                  Still no luck? We can help!
                </h6>
                <p className="text-base text-gray-600">
                  Contact us and we&apos;ll get back to you as soon as possible.
                </p>
              </div>
              <div className="grow-up">
                <PrimaryLinkButton
                  href="/contact"
                  text="Contact Us"
                  className="text-primary bg-secondary hover:bg-secondary/90"
                />
              </div>
            </div>
          </div>

          {/* FAQ Accordion */}
          <div className="lg:col-span-9">
            <div ref={accordionRef} className="space-y-0">
              {faqItems.map((item) => {
                const isExpanded = expandedItems.has(item.id);
                return (
                  <div
                    key={item.id}
                    className="accordion-item border-b border-gray-300 last:border-b-0"
                  >
                    {/* Question Header */}
                    <div className="accordion-header">
                      <button
                        className={`flex w-full items-center justify-between border-0 bg-transparent px-0 py-8 text-left transition-all duration-300 focus:ring-0 focus:outline-none ${
                          isExpanded ? "text-gray-900" : "text-gray-900"
                        }`}
                        type="button"
                        onClick={() => toggleItem(item.id)}
                        aria-expanded={isExpanded}
                        aria-controls={`collapse${item.id}`}
                      >
                        <span className="font-neue-haas pr-4 text-lg font-semibold text-gray-900 md:text-xl">
                          {item.question}
                        </span>
                        <ChevronDown
                          className={`h-5 w-5 flex-shrink-0 text-gray-600 transition-transform duration-300 ${
                            isExpanded ? "rotate-180" : "rotate-0"
                          }`}
                        />
                      </button>
                    </div>

                    {/* Answer Content */}
                    <div
                      className={`accordion-collapse overflow-hidden transition-all duration-300 ease-in-out ${
                        isExpanded ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
                      }`}
                      id={`collapse${item.id}`}
                      aria-labelledby={`heading${item.id}`}
                    >
                      <div className="accordion-body px-0 pb-8">
                        {item.id === "Eight" ? (
                          <div
                            className="text-base leading-relaxed text-gray-600 md:text-lg"
                            dangerouslySetInnerHTML={{ __html: item.answer }}
                          />
                        ) : (
                          <p className="text-base leading-relaxed text-gray-600 md:text-lg">
                            {item.answer}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FaqSection;
