import React from "react";
import Link from "next/link";
import Image from "next/image";
import { navigationItems } from "@/config/navigation";
import NavDropdown from "./nav-dropdown";
import LoginButton from "./auth/login-button";

const DesktopNav: React.FC = () => {
  return (
    <nav className="bg-primary fixed top-0 right-0 left-0 z-50 hidden h-24 pt-5 md:block">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between">
          <div className="flex-shrink-0">
            <Link href="/" className="flex items-center transition-transform hover:scale-105">
              <Image
                alt="epic-padel"
                title="epic-padel"
                src="/imgs/template/logo.svg"
                width={100}
                height={100}
                className="h-auto w-25"
              />
            </Link>
          </div>

          <div className="flex flex-1 justify-center">
            <nav className="flex items-center space-x-4 lg:space-x-8">
              <ul className="flex items-center space-x-3 lg:space-x-8">
                {navigationItems.map((item) => (
                  <NavDropdown key={item.label} item={item} />
                ))}
                <li>
                  <LoginButton />
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>

      {/* Decorative comet line */}
      <div className="absolute right-0 bottom-0 left-0 h-px bg-gradient-to-r from-transparent via-cyan-400 to-transparent opacity-30"></div>
    </nav>
  );
};

export default DesktopNav;
