import React from "react";
import Link from "next/link";
import Image from "next/image";
import LoginButton from "./auth/login-button";

interface MobileNavProps {
  onMenuClick: () => void;
}

const MobileNav: React.FC<MobileNavProps> = ({ onMenuClick }) => {
  return (
    <nav className="bg-primary/90 fixed top-0 right-0 left-0 z-50 block py-5 backdrop-blur-md md:hidden">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="flex items-center transition-transform hover:scale-105">
              <Image
                alt="epic-padel"
                title="epic-padel"
                src="/imgs/template/logo.svg?updatedAt=1747728314936&tr=w-100"
                width={100}
                height={100}
                className=""
              />
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <div className="flex items-center space-x-3">
            <button
              onClick={onMenuClick}
              className="flex items-center space-x-2 text-white transition-colors duration-200 hover:text-white/80"
            >
              <span className="font-helvetica text-lg font-bold">MENU</span>
              <Image
                className="transition-transform duration-200 hover:rotate-12"
                alt="menu"
                title="menu"
                src="/imgs/template/icons/menu1.svg"
                width={16}
                height={16}
              />
            </button>
            <LoginButton />
          </div>
        </div>
      </div>

      {/* Decorative comet line */}
      <div className="absolute right-0 bottom-0 left-0 h-px bg-gradient-to-r from-transparent via-cyan-400 to-transparent opacity-30"></div>
    </nav>
  );
};

export default MobileNav;
