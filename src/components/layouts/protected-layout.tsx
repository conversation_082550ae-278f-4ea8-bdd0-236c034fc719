"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/store/auth-store";
import { useUIStore } from "@/store/ui-store";
import Loader from "../loader";

interface ProtectedLayoutProps {
  children: React.ReactNode;
  requireAuth?: boolean;
}

const ProtectedLayout: React.FC<ProtectedLayoutProps> = ({ children, requireAuth = true }) => {
  const router = useRouter();
  const { isAuthenticated, isLoading, checkAuth, initializeAuth } = useAuthStore();
  const { openLoginDialog } = useUIStore();

  useEffect(() => {
    // Initialize auth on component mount
    initializeAuth();

    // Check auth status
    checkAuth();
  }, [initializeAuth, checkAuth]);

  useEffect(() => {
    if (!isLoading && requireAuth && !isAuthenticated) {
      // Redirect to home and open login dialog
      router.push("/");
      openLoginDialog();
    }
  }, [isLoading, requireAuth, isAuthenticated, router, openLoginDialog]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="bg-background flex min-h-screen items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader />
          <p className="text-primary">Loading...</p>
        </div>
      </div>
    );
  }

  // If auth is required but user is not authenticated, don't render children
  if (requireAuth && !isAuthenticated) {
    return null;
  }

  // Render children if authenticated or auth is not required
  return <>{children}</>;
};

export default ProtectedLayout;
