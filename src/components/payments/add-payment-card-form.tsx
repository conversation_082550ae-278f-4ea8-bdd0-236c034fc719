"use client";

import {
  CardCvcElement,
  CardExpiryElement,
  CardNumberElement,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";

import * as React from "react";
import { toast } from "sonner";
import PaymentFormSkeleton from "./payment-form-skeleton";
import { addPaymentMethod, createSetupIntent, useGetPaymentMethods } from "@/api/payment-service";
import Loader from "../loader";

const stripeElementContainerStyle =
  "flex h-10 w-full rounded-full border border-gray-300 px-3 py-2 text-sm focus-within:ring-2 focus-within:ring-primary focus-within:border-transparent focus-within:outline-none";

export function AddPaymentCardForm({ setOpen }: { setOpen: (open: boolean) => void }) {
  const stripe = useStripe();
  const elements = useElements();
  const [error, setError] = React.useState<string | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [country, setCountry] = React.useState("");
  const [cardBrand, setCardBrand] = React.useState<string | null>(null);
  const [cardholderName, setCardholderName] = React.useState("");
  const [clientSecret, setClientSecret] = React.useState<string | null>(null);
  const { revalidatePaymentMethods } = useGetPaymentMethods();

  const stripeElementStyle = {
    style: {
      base: {
        fontSize: "16px",
        fontWeight: "300",
        color: "#000000",
        "::placeholder": {
          color: "#666666",
        },
        padding: "10px 0",
      },
      invalid: {
        color: "#EF4444",
      },
    },
  };

  React.useEffect(() => {
    const fetchClientSecret = async () => {
      try {
        const response = await createSetupIntent();
        console.log("setup intent response", response);
        const secret = response?.data?.data?.client_secret;
        console.log("setup intent secret", secret);
        if (secret) {
          setClientSecret(secret);
        }
      } catch (error) {
        console.error("Error fetching setup intent:", error);
      }
    };

    fetchClientSecret();
  }, []);

  if (!clientSecret) {
    return <PaymentFormSkeleton />;
  }

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();
    try {
      if (!stripe || !elements || !country || !cardholderName.trim()) {
        setError("Please fill in all fields.");
        return;
      }

      setIsLoading(true);
      setError(null);

      const { error: paymentMethodError, paymentMethod } = await stripe.createPaymentMethod({
        type: "card",
        card: elements.getElement(CardNumberElement)!,
        billing_details: {
          name: cardholderName.trim(),
          address: {
            country,
          },
        },
      });

      if (paymentMethodError) {
        setError(paymentMethodError.message ?? "An error occurred. Please try again.");
        setIsLoading(false);
        return;
      }

      if (!clientSecret) {
        setError("Payment setup is not initialized.");
        return;
      }

      const { error: setupError, setupIntent } = await stripe.confirmCardSetup(clientSecret, {
        payment_method: paymentMethod.id,
      });

      if (setupError) {
        setError(setupError.message ?? "An error occurred while confirming card setup.");
        setIsLoading(false);
        return;
      }

      if (paymentMethod) {
        const response = await addPaymentMethod({
          paymentMethodId: paymentMethod.id,
        });

        if (response) {
          await revalidatePaymentMethods();
          toast.success("Payment method created successfully");
        }
      }

      setOpen(false);
    } catch (err) {
      setError("An error occurred while saving your card. Please try again.");
    }

    setIsLoading(false);
  }

  const handleCardChange = (event: any) => {
    setCardBrand(event.brand);
  };

  const getCardBrandIcon = (brand: string | null) => {
    switch (brand) {
      case "visa":
        return "https://js.stripe.com/v3/fingerprinted/img/visa-729c05c240c4bdb47b03ac81d9945bfe.svg";
      case "mastercard":
        return "https://js.stripe.com/v3/fingerprinted/img/mastercard-4d8844094130711885b5e41b28c9848f.svg";
      case "amex":
        return "https://js.stripe.com/v3/fingerprinted/img/amex-a49b82f46c5cd6a96a6e418a6ca1717c.svg";
      case "discover":
        return "https://js.stripe.com/v3/fingerprinted/img/discover-ac52cd46f89fa40a29a0bfb954e33173.svg";
      default:
        return "https://js.stripe.com/v3/fingerprinted/img/visa-729c05c240c4bdb47b03ac81d9945bfe.svg";
    }
  };

  return (
    <form onSubmit={onSubmit} className="space-y-6">
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">Cardholder Name</label>
        <input
          type="text"
          className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
          value={cardholderName}
          onChange={(e) => setCardholderName(e.target.value)}
          placeholder="Enter cardholder name"
          required
          disabled={isLoading}
        />
      </div>

      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">Card Number</label>
        <div className="relative">
          <div className={stripeElementContainerStyle}>
            <CardNumberElement
              options={stripeElementStyle}
              className="h-full w-full"
              onChange={handleCardChange}
            />
          </div>
          {cardBrand && (
            <div className="absolute top-1/2 right-3 -translate-y-1/2">
              <img
                src={getCardBrandIcon(cardBrand) || "/placeholder.svg"}
                alt={`${cardBrand} logo`}
                className="h-6 w-auto"
              />
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 gap-3 md:grid-cols-2 md:gap-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">Expiry Date</label>
          <div className={stripeElementContainerStyle}>
            <CardExpiryElement options={stripeElementStyle} className="h-full w-full" />
          </div>
        </div>
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">Security Code</label>
          <div className={stripeElementContainerStyle}>
            <CardCvcElement options={stripeElementStyle} className="h-full w-full font-normal" />
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">Country</label>
        <select
          className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
          value={country}
          onChange={(e) => setCountry(e.target.value)}
          required
          disabled={isLoading}
        >
          <option value="">Select Country</option>
          <option value="US">🇺🇸 United States</option>
        </select>
      </div>

      {error && <div className="mt-2 text-xs text-red-500">{error}</div>}

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          className="focus:ring-primary rounded-full border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-offset-2 focus:outline-none"
          onClick={() => setOpen(false)}
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="bg-primary hover:bg-primary/90 focus:ring-primary rounded-full border border-transparent px-4 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
          disabled={isLoading || !stripe}
        >
          {isLoading ? <Loader className="px-4" /> : "Save Payment Method"}
        </button>
      </div>
    </form>
  );
}
