"use client";

import React, { useState } from "react";
import MobileNavBar from "./mobile-nav-bar";
import DesktopNav from "./desktop-nav";
import MobileNav from "./mobile-nav";

const NavBar = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <>
      <DesktopNav />
      <MobileNav onMenuClick={toggleMobileMenu} />
      <MobileNavBar isOpen={isMobileMenuOpen} onClose={() => setIsMobileMenuOpen(false)} />
    </>
  );
};

export default NavBar;
