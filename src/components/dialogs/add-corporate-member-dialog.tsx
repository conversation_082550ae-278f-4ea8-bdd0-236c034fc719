import { addCorporateMember, addFamily<PERSON>ember } from "@/api/profile-service";
import { extractErrorMessage } from "@/libs/utils";
import * as Dialog from "@radix-ui/react-dialog";
import { X } from "lucide-react";
import React, { useState } from "react";
import { toast } from "sonner";

interface AddMemberDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onMemberAdded: () => void;
}

interface MemberFormData {
  name: string;
  email: string;
}

const AddCorporateMemberDialog: React.FC<AddMemberDialogProps> = ({
  isOpen,
  onClose,
  onMemberAdded,
}) => {
  const [isAddingMember, setIsAddingMember] = useState(false);
  const [memberFormData, setMemberFormData] = useState<MemberFormData>({
    name: "",
    email: "",
  });

  const handleMemberFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsAddingMember(true);
    try {
      const response = await addCorporateMember({
        name: memberFormData.name,
        email: memberFormData.email,
      });
      if (response) {
        toast.success("Corporate member added successfully!");
        onMemberAdded();
        onClose();
        setMemberFormData({ name: "", email: "" });
      }
    } catch (error: any) {
      console.error("Error adding member:", error);
      const errorMessage = extractErrorMessage(error);
      console.log("error message", errorMessage);
      toast.error(errorMessage || "Failed to add corporate member");
    } finally {
      setIsAddingMember(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      onClose();
    }
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={handleOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[2000] bg-black/50" />
        <Dialog.Content className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-[2001] w-full max-w-lg translate-x-[-50%] translate-y-[-50%] rounded-[20px] bg-white p-5 shadow-lg duration-200">
          <Dialog.Close className="focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-2 right-2 rounded-sm opacity-70 ring-0 transition-opacity hover:opacity-100 focus:ring-0 focus:outline-none disabled:pointer-events-none">
            <X className="h-8 w-8 text-gray-600 outline-none" />
            <span className="sr-only">Close</span>
          </Dialog.Close>

          <Dialog.Title className="text-lg font-medium text-gray-900">
            Add Corporate Member
          </Dialog.Title>
          <Dialog.Description className="mb-4 text-sm text-gray-500">
            Add a new corporate member to your account.
          </Dialog.Description>

          <form onSubmit={handleMemberFormSubmit} className="space-y-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Name</label>
                <input
                  type="text"
                  className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
                  value={memberFormData.name}
                  onChange={(e) => setMemberFormData({ ...memberFormData, name: e.target.value })}
                  required
                  placeholder="Enter Full Name"
                  disabled={isAddingMember}
                />
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Email</label>
                <input
                  type="email"
                  className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
                  value={memberFormData.email}
                  onChange={(e) => setMemberFormData({ ...memberFormData, email: e.target.value })}
                  required
                  placeholder="Enter Email"
                  disabled={isAddingMember}
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                className="focus:ring-primary rounded-full border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-offset-2 focus:outline-none"
                onClick={onClose}
                disabled={isAddingMember}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="bg-primary hover:bg-primary/90 focus:ring-primary rounded-full border border-transparent px-4 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                disabled={isAddingMember}
              >
                {isAddingMember ? "Adding Member..." : "Add Member"}
              </button>
            </div>
          </form>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default AddCorporateMemberDialog;
