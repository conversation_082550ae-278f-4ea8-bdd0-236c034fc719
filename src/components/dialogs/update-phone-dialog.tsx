import React, { useState } from "react";
import * as Dialog from "@radix-ui/react-dialog";
import { X } from "lucide-react";
import { toast } from "sonner";
import { updateUserEmailOrPhone, updateUserVerifyEmailOrPhone } from "@/api/profile-service";
import { useAuthStore } from "@/store/auth-store";

interface UpdatePhoneDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onPhoneUpdated: () => void;
  currentPhone?: string;
  currentCountryCode?: string;
}

const UpdatePhoneDialog: React.FC<UpdatePhoneDialogProps> = ({
  isOpen,
  onClose,
  onPhoneUpdated,
  currentPhone = "",
  currentCountryCode = "+1",
}) => {
  const updateUser = useAuthStore((state) => state.updateUser);
  const [phoneNumber, setPhoneNumber] = useState(currentPhone);
  const [countryCode, setCountryCode] = useState(currentCountryCode);
  const [showOTP, setShowOTP] = useState(false);
  const [otpValues, setOtpValues] = useState(["", "", "", ""]);
  const [phoneError, setPhoneError] = useState("");
  const [otpError, setOtpError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [timer, setTimer] = useState(600);
  const [isTimerActive, setIsTimerActive] = useState(false);

  // Timer effect
  React.useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isTimerActive && timer > 0) {
      interval = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);
    } else if (timer === 0) {
      setIsTimerActive(false);
    }
    return () => clearInterval(interval);
  }, [isTimerActive, timer]);

  // Format timer display
  const formatTimer = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  // Phone validation
  const validateUSPhone = (phone: string) => {
    const phoneRegex = /^[0-9]{10}$/;
    return phoneRegex.test(phone);
  };

  // Handle phone input
  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, "");
    const maxLength = countryCode === "+1" ? 10 : 9;
    if (value.length <= maxLength) {
      setPhoneNumber(value);
      setPhoneError("");
    }
  };

  // Handle OTP input
  const handleOtpChange = (index: number, value: string) => {
    if (value.length > 1) return;

    const newOtpValues = [...otpValues];
    newOtpValues[index] = value;
    setOtpValues(newOtpValues);
    setOtpError("");

    // Auto-focus next input
    if (value && index < 3) {
      const nextInput = document.querySelector(
        `input[data-otp-index="${index + 1}"]`
      ) as HTMLInputElement;
      if (nextInput) nextInput.focus();
    }
  };

  // Send OTP
  const handleSendOTP = async () => {
    const isValidPhone = validateUSPhone(phoneNumber);

    if (!isValidPhone) {
      setPhoneError("Invalid phone number");
      return;
    }

    setIsLoading(true);
    try {
      console.log("Sending OTP to:", countryCode + phoneNumber);

      const response = await updateUserEmailOrPhone({
        update: "PHONE",
        phone: phoneNumber,
        country: countryCode,
      });

      if (response) {
        toast.success(response?.message || "OTP sent successfully!");
      } else {
        toast.error(response?.message || "Failed to send OTP");
      }

      setShowOTP(true);
      setTimer(600);
      setIsTimerActive(true);
    } catch (error: any) {
      console.error("Error sending OTP:", error);
      toast.error(error?.message || "Failed to send OTP");
      setPhoneError("Failed to send OTP. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Verify OTP
  const handleVerifyOTP = async () => {
    const otp = otpValues.join("");
    if (otp.length !== 4) {
      setOtpError("Please enter a valid 4-digit OTP");
      return;
    }

    setIsLoading(true);
    try {
      console.log("Verifying OTP:", otp);

      const response = await updateUserVerifyEmailOrPhone({
        update: "PHONE",
        phone: phoneNumber,
        country: countryCode,
        otp: otp,
      });

      if (response) {
        toast.success(response.message || "Phone number updated successfully!");
        updateUser({ user_phone: { phone: phoneNumber, country_code: countryCode } });
      } else {
        toast.error(response.message || "Failed to update phone number");
      }

      onPhoneUpdated();
      handleClose();
    } catch (error: any) {
      console.error("Error verifying OTP:", error);
      toast.error(error?.message || "Failed to verify OTP");
      setOtpError("Invalid OTP. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setPhoneNumber(currentPhone);
    setCountryCode(currentCountryCode);
    setShowOTP(false);
    setOtpValues(["", "", "", ""]);
    setPhoneError("");
    setOtpError("");
    setIsLoading(false);
    setTimer(600);
    setIsTimerActive(false);
    onClose();
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      handleClose();
    }
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={handleOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[2000] bg-black/50" />
        <Dialog.Content className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-[2001] w-full max-w-lg translate-x-[-50%] translate-y-[-50%] rounded-[20px] bg-white p-5 shadow-lg duration-200">
          <Dialog.Close className="focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-2 right-2 rounded-sm opacity-70 ring-0 transition-opacity hover:opacity-100 focus:ring-0 focus:outline-none disabled:pointer-events-none">
            <X className="h-8 w-8 text-gray-600 outline-none" />
            <span className="sr-only">Close</span>
          </Dialog.Close>

          <Dialog.Title className="text-lg font-medium text-gray-900">
            Update Phone Number
          </Dialog.Title>
          <Dialog.Description className="mb-4 text-sm text-gray-500">
            Enter your new phone number and verify it with OTP.
          </Dialog.Description>

          {!showOTP ? (
            // Phone Input Screen
            <>
              <div className="mb-4">
                <div className="flex overflow-hidden rounded-full border border-gray-300">
                  <select
                    value={countryCode}
                    onChange={(e) => setCountryCode(e.target.value)}
                    className="border-r border-gray-300 bg-gray-50 px-3 py-3 text-sm focus:outline-none"
                  >
                    <option value="+1">🇺🇸 +1</option>
                  </select>
                  <input
                    type="tel"
                    placeholder="Mobile Number"
                    value={phoneNumber}
                    onChange={handlePhoneChange}
                    maxLength={countryCode === "+1" ? 10 : 9}
                    className="flex-1 px-3 py-3 text-black focus:outline-none"
                  />
                </div>
                {phoneError && <div className="mt-1 text-sm text-red-500">{phoneError}</div>}
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  className="focus:ring-primary rounded-full border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-offset-2 focus:outline-none"
                  onClick={handleClose}
                >
                  Cancel
                </button>
                <button
                  onClick={handleSendOTP}
                  disabled={isLoading}
                  className="bg-primary hover:bg-primary/90 focus:ring-primary rounded-full border border-transparent px-4 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                >
                  {isLoading ? "Sending..." : "Send OTP"}
                </button>
              </div>
            </>
          ) : (
            // OTP Input Screen
            <>
              <div className="mb-4">
                <div className="mb-4 flex justify-center space-x-3">
                  {otpValues.map((value, index) => (
                    <input
                      key={index}
                      type="text"
                      value={value}
                      onChange={(e) => handleOtpChange(index, e.target.value)}
                      maxLength={1}
                      className="focus:ring-primary h-12 w-12 rounded-lg border border-gray-300 text-center text-black focus:ring-2 focus:outline-none"
                      data-otp-index={index}
                    />
                  ))}
                </div>
                {otpError && (
                  <div className="mb-2 text-center text-sm text-red-500">{otpError}</div>
                )}
                <div className="text-center text-sm text-gray-600">
                  Code will expire in <span className="font-medium">{formatTimer(timer)}</span>{" "}
                  seconds.
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  className="focus:ring-primary rounded-full border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-offset-2 focus:outline-none"
                  onClick={handleClose}
                >
                  Cancel
                </button>
                <button
                  onClick={handleVerifyOTP}
                  disabled={isLoading}
                  className="bg-primary hover:bg-primary/90 focus:ring-primary rounded-full border border-transparent px-4 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                >
                  {isLoading ? "Verifying..." : "Verify OTP"}
                </button>
              </div>
            </>
          )}
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default UpdatePhoneDialog;
