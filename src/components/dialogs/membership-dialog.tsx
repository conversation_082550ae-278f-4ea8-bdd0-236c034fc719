"use client";

import { purchaseSetup, updateUserProfile } from "@/api/profile-service";
import { useFacebookPixel } from "@/hooks/use-facebook-pixel";
import { useSignaturePad } from "@/hooks/use-signature-pad";
import { useAuthStore } from "@/store/auth-store";
import { useUIStore } from "@/store/ui-store";
import * as Dialog from "@radix-ui/react-dialog";
import { Trash2, X } from "lucide-react";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import { z } from "zod";
import TermsAndConditionsContent from "../term-and-conditions-content";

// Zod schema for form validation
const membershipFormSchema = z.object({
  name: z.string().min(1, "Full name is required"),
  email: z.string().email("Please enter a valid email address"),
  phone: z
    .string()
    .min(1, "Phone number is required")
    .refine((val) => /^(?:[0-9][0-9]{2})?[0-9]{7}$/.test(val), {
      message: "Please enter a valid US phone number",
    }),
  countryCode: z.string().default("+1"),
  isAbove18: z.boolean().optional(),
  isGuardian: z.boolean().optional(),
});

type MembershipFormData = z.infer<typeof membershipFormSchema>;

interface MembershipDialogProps {}

interface SignatureError {
  signature?: string;
}

const MembershipDialog: React.FC<MembershipDialogProps> = () => {
  const { isMembershipDialogOpen, closeMembershipDialog, selectedPlan } = useUIStore();
  console.log("selectedPlan", selectedPlan);
  const { user, updateUser } = useAuthStore();
  const { trackAddPaymentInfo } = useFacebookPixel();
  const [formData, setFormData] = useState<MembershipFormData>({
    name: user?.name || "",
    email: user?.user_email?.email || "",
    phone: user?.user_phone?.phone || "",
    countryCode: user?.user_phone?.country_code || "+1",
    isAbove18: false,
    isGuardian: false,
  });
  const [errors, setErrors] = useState<Partial<Record<keyof MembershipFormData, string>>>({});
  const [signatureError, setSignatureError] = useState<SignatureError>({});
  const [isLoading, setIsLoading] = useState(false);

  const {
    canvasRef,
    isEmpty: isSignatureEmpty,
    clearSignature,
    getSignatureData,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleMouseLeave,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
  } = useSignaturePad();

  // Pre-fill form data when dialog is opened and user is available
  useEffect(() => {
    if (isMembershipDialogOpen && user) {
      let isAbove18 = false;
      if (user.dob) {
        const dob = new Date(user.dob);
        const today = new Date();
        let age = today.getFullYear() - dob.getFullYear();
        const m = today.getMonth() - dob.getMonth();
        if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) {
          age--;
        }
        isAbove18 = age >= 18;
      }
      setFormData({
        name: user.name || "",
        email: user.user_email?.email || "",
        phone: user.user_phone?.phone || "",
        countryCode: user.user_phone?.country_code || "+1",
        isAbove18,
        isGuardian: false,
      });
      setErrors({});
      setSignatureError({});
    }
  }, [isMembershipDialogOpen, user]);

  const handleInputChange = (field: keyof MembershipFormData, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, ""); // Remove non-digits
    const maxLength = formData.countryCode === "+1" ? 10 : 9;
    if (value.length <= maxLength) {
      handleInputChange("phone", value);
    }
  };

  // Helper to determine which checkbox to show
  const showGuardianCheckbox = selectedPlan && selectedPlan.max_age && selectedPlan.max_age <= 18;
  const showAbove18Checkbox = !showGuardianCheckbox;

  const validateForm = (): boolean => {
    let valid = true;
    let newErrors: Partial<Record<keyof MembershipFormData, string>> = {};
    try {
      membershipFormSchema.parse(formData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        error.errors.forEach((err) => {
          const field = err.path[0] as keyof MembershipFormData;
          newErrors[field] = err.message;
        });
      }
      valid = false;
    }
    // Only require the visible checkbox
    if (showGuardianCheckbox && !formData.isGuardian) {
      newErrors.isGuardian = "You must confirm you are a guardian/parent for this package.";
      valid = false;
    }
    if (showAbove18Checkbox && !formData.isAbove18) {
      newErrors.isAbove18 = "You must confirm you are above 18 for this package.";
      valid = false;
    }
    // Age range validation for selectedPlan
    // if (selectedPlan && user?.dob) {
    //   const minAge = selectedPlan.min_age ?? 0;
    //   const maxAge = selectedPlan.max_age ?? 0;
    //   if ((minAge || maxAge) && !(minAge === 0 && maxAge === 0)) {
    //     const dob = new Date(user.dob);
    //     const today = new Date();
    //     let age = today.getFullYear() - dob.getFullYear();
    //     const m = today.getMonth() - dob.getMonth();
    //     if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) {
    //       age--;
    //     }
    //     if ((minAge && age < minAge) || (maxAge && maxAge > 0 && age > maxAge)) {
    //       if (showAbove18Checkbox)
    //         newErrors.isAbove18 = `Your age does not meet the requirements for this subscription (Allowed: ${minAge || 0} - ${maxAge || "No limit"} years).`;
    //       if (showGuardianCheckbox)
    //         newErrors.isGuardian = `Your age does not meet the requirements for this subscription (Allowed: ${minAge || 0} - ${maxAge || "No limit"} years).`;
    //       valid = false;
    //     }
    //   }
    // }
    setErrors(newErrors);
    return valid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (isSignatureEmpty) {
      setSignatureError({ signature: "Signature is required" });
      return;
    }

    setIsLoading(true);
    try {
      const signatureData = getSignatureData();

      console.log("Submitting membership form:", {
        ...formData,
        signature: signatureData,
        plan: selectedPlan,
      });

      const updateUserProfileResponse = await updateUserProfile({
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        country_code: formData.countryCode,
      });

      if (updateUserProfileResponse) {
        updateUser({
          name: formData.name,
          user_email: {
            email: formData.email,
          },
          user_phone: {
            phone: formData.phone,
            country_code: formData.countryCode,
          },
        });
      }

      const purchaseSetupResponse = await purchaseSetup({
        package_id: selectedPlan?.id,
        frequency: selectedPlan?.isYearly ? "M" : "Y",
        name: formData.name,
        email: formData.email,
        phone: formData.countryCode + formData.phone,
        signature: signatureData,
      });

      // console.log("purchaseSetupResponse", purchaseSetupResponse);

      if (purchaseSetupResponse) {
        const stripeURL = purchaseSetupResponse?.data?.remarks;
        console.log("stripeURL", stripeURL);
        trackAddPaymentInfo(selectedPlan?.id.toString() || "");
        // sendFacebookConversionApiEvent("AddPaymentInfo", {
        //   email: "<EMAIL>",
        //   phone: "1234567890",
        // });
        if (window && stripeURL) {
          window.location.href = stripeURL;
        }
      }

      handleClose();
    } catch (error: any) {
      console.error("Error submitting form:", error);
      toast.error(error?.errors?.email?.[0] || error.message || "Error submitting form");
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    // Do not reset formData to empty, just clear errors and signature
    setErrors({});
    setSignatureError({});
    clearSignature();
    closeMembershipDialog();
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      handleClose();
    }
  };

  return (
    <Dialog.Root open={isMembershipDialogOpen} onOpenChange={handleOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[2000] bg-black/50" />
        <Dialog.Content className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-[2001] max-h-[90vh] w-full max-w-4xl translate-x-[-50%] translate-y-[-50%] overflow-hidden rounded-[20px] bg-white shadow-lg duration-200">
          <Dialog.Close className="focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 z-10 rounded-sm opacity-70 ring-0 transition-opacity hover:opacity-100 focus:ring-0 focus:outline-none disabled:pointer-events-none">
            <X className="h-8 w-8 text-gray-600 outline-none" />
            <span className="sr-only">Close</span>
          </Dialog.Close>

          <Dialog.Title></Dialog.Title>

          <div className="flex h-full max-h-[90vh] flex-col">
            {/* Header */}
            <div className="border-b border-gray-200 px-6 py-3">
              <h2 className="mb-2 text-2xl font-bold text-gray-900">Terms And Conditions</h2>
              {/* {selectedPlan && (
                <p className="text-sm text-gray-600">
                  Plan: {selectedPlan.name} - ${selectedPlan.price}/
                  {selectedPlan.isYearly ? "year" : "month"}
                </p>
              )} */}
            </div>

            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto p-6">
              {/* Terms Content */}
              <TermsAndConditionsContent />

              {/* Form */}
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-1">
                  {/* Only one checkbox shown at a time based on selectedPlan.max_age */}
                  {showAbove18Checkbox ? (
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        id="age"
                        checked={formData.isAbove18}
                        // disabled={user?.dob ? true : false}
                        onChange={(e) => handleInputChange("isAbove18", e.target.checked)}
                        className="h-4 w-4"
                      />
                      <label htmlFor="age" className="text-sm text-gray-700">
                        Are you above 18?
                      </label>
                      {errors.isAbove18 && (
                        <span className="text-sm text-red-500">{errors.isAbove18}</span>
                      )}
                    </div>
                  ) : null}
                  {showGuardianCheckbox ? (
                    <div className={`flex items-center gap-2`}>
                      <input
                        type="checkbox"
                        id="guardian"
                        checked={formData.isGuardian}
                        onChange={(e) => handleInputChange("isGuardian", e.target.checked)}
                        className="h-4 w-4"
                      />
                      <label htmlFor="guardian" className="text-sm text-gray-700">
                        I am the parent/legal guardian
                      </label>
                      {errors.isGuardian && (
                        <span className="text-sm text-red-500">{errors.isGuardian}</span>
                      )}
                    </div>
                  ) : null}
                </div>

                {/* Form Fields */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  {/* Name */}
                  <div className="flex flex-col">
                    <input
                      type="text"
                      id="name"
                      placeholder="Enter Your Full Name"
                      value={formData.name}
                      onChange={(e) => handleInputChange("name", e.target.value)}
                      className="focus:ring-primary w-full rounded-full border border-black px-4 py-3 text-black focus:ring-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                    />
                    {errors.name && (
                      <span className="mt-1 text-sm text-red-500">{errors.name}</span>
                    )}
                  </div>

                  {/* Email */}
                  <div className="flex flex-col">
                    <input
                      type="email"
                      id="email"
                      placeholder="Enter Email"
                      value={formData.email}
                      disabled={user?.user_email?.email ? true : false}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      className="focus:ring-primary w-full rounded-full border border-black px-4 py-3 text-black focus:ring-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                    />
                    {errors.email && (
                      <span className="mt-1 text-sm text-red-500">{errors.email}</span>
                    )}
                  </div>

                  {/* Phone */}
                  <div className="flex flex-col">
                    <div className="focus:ring-primary flex overflow-hidden rounded-full border border-black focus:ring-2">
                      <select
                        value={formData.countryCode}
                        onChange={(e) => handleInputChange("countryCode", e.target.value)}
                        disabled={user?.user_phone?.phone ? true : false}
                        className="border-r border-black bg-gray-50 px-3 py-3 text-black focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="+1">🇺🇸 +1</option>
                      </select>
                      <input
                        type="tel"
                        placeholder="Enter Phone Number"
                        value={formData.phone}
                        onChange={handlePhoneChange}
                        disabled={user?.user_phone?.phone ? true : false}
                        maxLength={formData.countryCode === "+1" ? 10 : 9}
                        className="flex-1 px-4 py-3 text-black focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                      />
                    </div>
                    {errors.phone && (
                      <span className="mt-1 text-sm text-red-500">{errors.phone}</span>
                    )}
                  </div>
                </div>

                {/* Signature */}
                <div className="flex flex-col items-center space-y-2">
                  <label className="text-sm font-medium text-gray-700">Signature</label>
                  <div className="relative">
                    <canvas
                      ref={canvasRef}
                      width={400}
                      height={100}
                      className="h-[100px] w-full max-w-[400px] cursor-crosshair touch-none rounded-lg border border-black md:w-[400px]"
                      style={{ maxWidth: "400px", height: "100px" }}
                      onMouseDown={handleMouseDown}
                      onMouseMove={handleMouseMove}
                      onMouseUp={handleMouseUp}
                      onMouseLeave={handleMouseLeave}
                      onTouchStart={handleTouchStart}
                      onTouchMove={handleTouchMove}
                      onTouchEnd={handleTouchEnd}
                    />
                    <button
                      type="button"
                      onClick={clearSignature}
                      className="absolute top-2 right-2 p-1 text-gray-500 transition-colors hover:text-red-500"
                      title="Clear signature"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                  {signatureError.signature && (
                    <span className="text-sm text-red-500">{signatureError.signature}</span>
                  )}
                </div>

                {/* Submit Button */}
                <div className="flex justify-center pt-2">
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="bg-primary hover:bg-primary/90 focus:ring-primary rounded-full px-8 py-3 font-medium text-white transition-colors focus:ring-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    {isLoading ? "Processing..." : "Agree"}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default MembershipDialog;
