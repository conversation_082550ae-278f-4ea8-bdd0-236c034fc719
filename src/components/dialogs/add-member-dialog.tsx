import * as Dialog from "@radix-ui/react-dialog";
import { X } from "lucide-react";
import React, { useState } from "react";
import { StripeProvider } from "../providers/stripe-provider";
import AddFamilyMemberForm, { type MemberFormData } from "../forms/add-family-member-form";
import { AddPaymentCardForm } from "../payments/add-payment-card-form";

interface AddMemberDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onMemberAdded: () => void;
}

const AddMemberDialog: React.FC<AddMemberDialogProps> = ({ isOpen, onClose, onMemberAdded }) => {
  const [currentForm, setCurrentForm] = useState<"member" | "payment">("member");

  // Persistent form data that survives form switches
  const [persistentFormData, setPersistentFormData] = useState<MemberFormData>({
    name: "",
    email: "",
    dob: "",
    phone: "",
    country: "+1",
    paymentMethodId: "",
    makeDefault: false,
  });

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      setCurrentForm("member"); // Reset to member form when closing
      // Reset form data when dialog closes
      setPersistentFormData({
        name: "",
        email: "",
        dob: "",
        phone: "",
        country: "+1",
        paymentMethodId: "",
        makeDefault: false,
      });
      onClose();
    }
  };

  const handleSuccess = () => {
    onMemberAdded();
    setCurrentForm("member"); // Reset to member form after success
    // Reset form data after successful submission
    setPersistentFormData({
      name: "",
      email: "",
      dob: "",
      phone: "",
      country: "+1",
      paymentMethodId: "",
      makeDefault: false,
    });
    onClose();
  };

  const handleShowPaymentForm = () => {
    setCurrentForm("payment");
  };

  const handleBackToMemberForm = (open: boolean) => {
    if (!open) {
      setCurrentForm("member");
    }
  };

  return (
    <StripeProvider>
      <Dialog.Root open={isOpen} onOpenChange={handleOpenChange}>
        <Dialog.Portal>
          <Dialog.Overlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[2000] bg-black/50" />
          <Dialog.Content className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-[2001] w-full max-w-lg translate-x-[-50%] translate-y-[-50%] rounded-[20px] bg-white p-5 shadow-lg duration-200">
            <Dialog.Close className="focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-2 right-2 rounded-sm opacity-70 ring-0 transition-opacity hover:opacity-100 focus:ring-0 focus:outline-none disabled:pointer-events-none">
              <X className="h-8 w-8 text-gray-600 outline-none" />
              <span className="sr-only">Close</span>
            </Dialog.Close>

            <Dialog.Title className="text-lg font-medium text-gray-900">
              {currentForm === "member" ? "Add Family Member" : "Add Payment Method"}
            </Dialog.Title>
            <Dialog.Description className="mb-4 text-sm text-gray-500">
              {currentForm === "member"
                ? "Add a new family member to your account."
                : "Add a new payment method to your account."}
            </Dialog.Description>

            {currentForm === "member" ? (
              <AddFamilyMemberForm
                onSuccess={handleSuccess}
                onCancel={onClose}
                onShowPaymentForm={handleShowPaymentForm}
                initialFormData={persistentFormData}
                onFormDataChange={setPersistentFormData}
              />
            ) : (
              <AddPaymentCardForm setOpen={handleBackToMemberForm} />
            )}
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>
    </StripeProvider>
  );
};

export default AddMemberDialog;
