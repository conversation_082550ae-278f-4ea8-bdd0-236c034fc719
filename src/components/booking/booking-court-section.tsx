"use client";
import { useBookingStore } from "@/store/booking-store";
import { SportType } from "@/types/booking";
import { useCallback } from "react";

const BookingCourtSection = () => {
  const { selectedSport, setSelectedSport } = useBookingStore();

  const handleSportSelect = useCallback(
    (sport: SportType) => {
      setSelectedSport(sport);
    },
    [setSelectedSport]
  );

  const sportItems = [
    {
      name: "Padel",
      image: "/imgs/booking/padel.png",
      comingSoon: false,
    },
    {
      name: "Pickleball",
      image: "/imgs/booking/pickleball.png",
      comingSoon: true,
    },
    {
      name: "Tennis",
      image: "/imgs/booking/tennis.png",
      comingSoon: true,
    },
  ];

  return (
    <div className="flex w-full flex-col items-center">
      <div className="w-full flex-col items-center justify-start">
        <h1 className="font-helvetica justify-center text-center text-4xl font-bold text-[#1c5534]">
          Booking Options
        </h1>
        <p className="font-helvetica justify-center self-stretch text-center font-normal text-[#364153]">
          Choose how you play. Book the experience that fits you.
        </p>
      </div>

      <div className="container flex w-full items-center justify-start gap-10 py-6">
        {sportItems.map((sport) => (
          <div
            key={sport.name}
            className="relative flex h-[92px] flex-1 cursor-pointer items-center justify-center gap-2.5 rounded-[10px] p-2.5 transition-all duration-200"
            onClick={() => handleSportSelect(sport.name as SportType)}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                e.preventDefault();
                handleSportSelect(sport.name as SportType);
              }
            }}
          >
            {/* Background layer with opacity */}
            <div
              className="absolute inset-0 rounded-[10px] transition-all duration-500"
              style={{
                backgroundImage: `url(${sport.image})`,
                backgroundRepeat: "no-repeat",
                objectFit: "cover",
                backgroundSize: "cover",
                backgroundPosition: "center",
                opacity: selectedSport === sport.name ? 1 : 0.1,
                backgroundBlendMode: "luminosity",
                backgroundColor: selectedSport === sport.name ? "transparent" : "#F5F5F5",
              }}
            />

            {/* Text layer with full opacity */}
            <div className="relative z-10 text-center">
              <p
                className={`text-2xl font-bold ${
                  selectedSport === sport.name ? "text-white" : "text-[#9F9F9F]"
                }`}
              >
                {sport.name}
              </p>
              {sport.comingSoon && (
                <p
                  className={`text-sm ${
                    selectedSport === sport.name ? "text-white" : "text-[#9F9F9F]"
                  }`}
                >
                  Coming Soon
                </p>
              )}
            </div>
          </div>
        ))}
      </div>
      <div
        data-property-1="Default"
        className="inline-flex h-[68px] w-[1234px] items-center justify-start gap-2.5 rounded-[100px] bg-neutral-100 p-2.5"
      >
        <div className="flex flex-1 items-center justify-center gap-2.5 self-stretch rounded-[30px] bg-[#1c5534] p-2.5">
          <div className="justify-center text-center font-['Helvetica'] text-base leading-[41px] font-bold tracking-tight text-white">
            Book a Court
          </div>
        </div>
        <div className="flex flex-1 items-center justify-center gap-2.5 self-stretch rounded-[60px] p-2.5">
          <div className="justify-center text-center font-['Helvetica'] text-base leading-[41px] font-bold tracking-tight text-[#969696]">
            Programs
          </div>
        </div>
        <div className="flex flex-1 items-center justify-center gap-2.5 self-stretch rounded-[60px] p-2.5">
          <div className="justify-center text-center font-['Helvetica'] text-base leading-[41px] font-bold tracking-tight text-[#969696]">
            Lessons
          </div>
        </div>
        <div className="flex flex-1 items-center justify-center gap-2.5 self-stretch rounded-[60px] p-2.5">
          <div className="justify-center text-center font-['Helvetica'] text-base font-bold tracking-tight text-[#969696]">
            Instructors
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingCourtSection;
