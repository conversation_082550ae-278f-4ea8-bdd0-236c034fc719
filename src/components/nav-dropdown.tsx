import React from "react";
import Link from "next/link";
import { ChevronDown } from "lucide-react";
import { NavItem } from "@/config/navigation";

interface NavDropdownProps {
  item: NavItem;
}

const NavDropdown: React.FC<NavDropdownProps> = ({ item }) => {
  return (
    <li className="group relative">
      <Link
        href={item.href || "#"}
        className="font-helvetica flex items-center py-6 text-sm font-medium text-white transition-colors duration-200 hover:text-white/80 lg:text-base"
      >
        {item.label}
        {item.children && (
          <ChevronDown className="ml-0.5 h-5 w-5 transition-transform duration-300 group-hover:rotate-180" />
        )}
      </Link>
      {item.children && (
        <ul className="bg-primary/90 invisible absolute top-full left-0 mt-2 w-56 rounded-3xl border border-green-700 p-3 opacity-0 shadow-lg backdrop-blur-md transition-all duration-200 group-hover:visible group-hover:opacity-100">
          {item.children.map((child, index) => (
            <li key={child.label} className={index !== item.children!.length - 1 ? "mb-1" : ""}>
              <Link
                href={child.href || "#"}
                className="font-helvetica block rounded-full px-6 py-3 text-sm font-medium text-white transition-colors duration-200 hover:bg-white/10"
              >
                {child.label}
              </Link>
            </li>
          ))}
        </ul>
      )}
    </li>
  );
};

export default NavDropdown;
