import localFont from "next/font/local";

export const helvetica = localFont({
  src: [
    {
      path: "../../public/fonts/helvetica/HelveticaNeueRoman.otf",
      weight: "400",
      style: "normal",
    },
    {
      path: "../../public/fonts/helvetica/HelveticaNeueMedium.otf",
      weight: "500",
      style: "normal",
    },
    {
      path: "../../public/fonts/Helvetica.ttf",
      weight: "600",
      style: "normal",
    },
    {
      path: "../../public/fonts/helvetica-rounded-bold-5871d05ead8de-webfont.woff2",
      weight: "700",
      style: "normal",
    },
    {
      path: "../../public/fonts/helvetica-webfont.woff2",
      weight: "normal",
      style: "normal",
    },
  ],
  variable: "--font-helvetica",
});

export const neueHaasUnica = localFont({
  src: [
    {
      path: "../../public/fonts/<PERSON>eue-Haas-Unica-W1G.woff2",
      weight: "400",
      style: "normal",
    },
    {
      path: "../../public/fonts/Neue-Haas-Unica-W1G-bold.otf",
      weight: "800",
      style: "normal",
    },
  ],
  variable: "--font-neue-haas",
});
