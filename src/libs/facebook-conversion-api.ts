import axios from 'axios';
import SHA256 from 'crypto-js/sha256';

const hash = (value: string) => SHA256(value.trim().toLowerCase()).toString();

export const sendFacebookConversionApiEvent = async (
    eventName: string,
    userData?: {
        email?: string; phone?: string
    },
    eventData?: Record<string, any>
) => {
    try {
        const payload = {
            data: [
                {
                    event_name: eventName,
                    event_time: Math.floor(Date.now() / 1000),
                    action_source: 'website',
                    event_source_url: window.location.href,
                    user_data: {
                        em: userData?.email ? [hash(userData.email)] : undefined,
                        ph: userData?.phone ? [hash(userData.phone)] : undefined,
                        client_ip_address: undefined, // Not accessible from browser
                        client_user_agent: navigator.userAgent,
                    },
                    custom_data: eventData || {},
                },
            ],
            access_token: process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ACCESS_TOKEN!,
        };

        const response = await axios.post(
            `https://graph.facebook.com/v23.0/${process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID!}/events`,
            payload
        );

        console.log('Facebook CAPI response:', response.data);
    } catch (err) {
        console.error('Facebook CAPI error:', err);
    }
};