declare global {
  interface Window {
    fbq: any;
  }
}

// Facebook Pixel tracking utilities
export const FacebookPixelEvents = {
  // Track when user completes registration
  trackCompleteRegistration: () => {
    if (typeof window !== "undefined" && window.fbq) {
      window.fbq("track", "CompleteRegistration");
    }
  },

  // Track when user initiates checkout
  trackInitiateCheckout: (planId: string, price: number, planName: string, frequency: string) => {
    if (typeof window !== "undefined" && window.fbq) {
      console.log("trackInitiateCheckout", planId, price, planName, frequency);
      window.fbq("track", "InitiateCheckout", {
        planId: planId,
        price: price,
        planName: planName,
        frequency: frequency,
      });
    }
  },

  // Track when user adds payment info
  trackAddPaymentInfo: (planId: string) => {
    if (typeof window !== "undefined" && window.fbq) {
      window.fbq("track", "AddPaymentInfo", {
        planId: planId,
      });
    }
  },

  // Track when user subscribes
  trackSubscribe: () => {
    if (typeof window !== "undefined" && window.fbq) {
      window.fbq("track", "Subscribe");
    }
  },

  // Track when user contacts
  trackContact: () => {
    if (typeof window !== "undefined" && window.fbq) {
      window.fbq("track", "Contact");
    }
  },

  // Track custom events
  trackCustomEvent: (eventName: string, parameters?: Record<string, any>) => {
    if (typeof window !== "undefined" && window.fbq) {
      window.fbq("track", eventName, parameters);
    }
  },

  // Track page view (usually handled automatically)
  trackPageView: () => {
    if (typeof window !== "undefined" && window.fbq) {
      window.fbq("track", "PageView");
    }
  },

  // Track view content
  trackViewContent: (contentType?: string, contentId?: string) => {
    if (typeof window !== "undefined" && window.fbq) {
      const params: Record<string, any> = {};
      if (contentType) params.content_type = contentType;
      if (contentId) params.content_ids = [contentId];
      
      window.fbq("track", "ViewContent", Object.keys(params).length > 0 ? params : undefined);
    }
  },
};

// Export individual functions for backward compatibility
export const {
  trackCompleteRegistration,
  trackInitiateCheckout,
  trackAddPaymentInfo,
  trackSubscribe,
  trackContact,
  trackCustomEvent,
  trackPageView,
  trackViewContent,
} = FacebookPixelEvents;
