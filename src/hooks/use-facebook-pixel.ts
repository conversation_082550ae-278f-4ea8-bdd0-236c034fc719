"use client";

import { FacebookPixelEvents } from "@/libs/facebook-pixel";

/**
 * Custom hook for Facebook Pixel tracking
 * Provides easy access to all tracking functions
 */
export const useFacebookPixel = () => {
  return {
    // Registration tracking
    trackCompleteRegistration: FacebookPixelEvents.trackCompleteRegistration,

    // E-commerce tracking
    trackInitiateCheckout: FacebookPixelEvents.trackInitiateCheckout,
    trackAddPaymentInfo: FacebookPixelEvents.trackAddPaymentInfo,
    trackSubscribe: FacebookPixelEvents.trackSubscribe,

    // Engagement tracking
    trackContact: FacebookPixelEvents.trackContact,
    trackViewContent: FacebookPixelEvents.trackViewContent,

    // Custom tracking
    trackCustomEvent: FacebookPixelEvents.trackCustomEvent,
    trackPageView: FacebookPixelEvents.trackPageView,
  };
};
