import { useRef, useEffect, useState } from "react";

interface Position {
  x: number;
  y: number;
}

export const useSignaturePad = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [lastPosition, setLastPosition] = useState<Position | null>(null);
  const [isEmpty, setIsEmpty] = useState(true);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Initialize canvas styles
    ctx.strokeStyle = "black";
    ctx.lineWidth = 2;
    ctx.lineCap = "round";
    ctx.lineJoin = "round";

    // Set canvas size based on actual dimensions
    const rect = canvas.getBoundingClientRect();
    const dpr = window.devicePixelRatio || 1;

    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;

    // Scale the context to match device pixel ratio
    ctx.scale(dpr, dpr);

    // Re-apply styles after scaling
    ctx.strokeStyle = "black";
    ctx.lineWidth = 2;
    ctx.lineCap = "round";
    ctx.lineJoin = "round";
  }, []);

  const getPosition = (evt: { clientX: number; clientY: number }): Position => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    return {
      x: evt.clientX - rect.left,
      y: evt.clientY - rect.top,
    };
  };

  const startDraw = (evt: { clientX: number; clientY: number }) => {
    setIsDrawing(true);
    setLastPosition(getPosition(evt));
    setIsEmpty(false);
  };

  const stopDraw = () => {
    setIsDrawing(false);
    setLastPosition(null);
  };

  const draw = (evt: { clientX: number; clientY: number }) => {
    if (!isDrawing || !lastPosition) return;

    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    if (!canvas || !ctx) return;

    const currentPosition = getPosition(evt);

    ctx.beginPath();
    ctx.moveTo(lastPosition.x, lastPosition.y);
    ctx.lineTo(currentPosition.x, currentPosition.y);
    ctx.stroke();

    setLastPosition(currentPosition);
  };

  const clearSignature = () => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    if (!canvas || !ctx) return;

    const rect = canvas.getBoundingClientRect();
    const dpr = window.devicePixelRatio || 1;

    ctx.clearRect(0, 0, rect.width * dpr, rect.height * dpr);
    setIsEmpty(true);
  };

  const getSignatureData = (): string | null => {
    const canvas = canvasRef.current;
    if (!canvas || isEmpty) return null;

    return canvas.toDataURL();
  };

  // Mouse event handlers
  const handleMouseDown = (evt: React.MouseEvent<HTMLCanvasElement>) => {
    startDraw(evt.nativeEvent);
  };

  const handleMouseMove = (evt: React.MouseEvent<HTMLCanvasElement>) => {
    draw(evt.nativeEvent);
  };

  const handleMouseUp = () => {
    stopDraw();
  };

  const handleMouseLeave = () => {
    stopDraw();
  };

  // Touch event handlers
  const handleTouchStart = (evt: React.TouchEvent<HTMLCanvasElement>) => {
    // evt.preventDefault();
    if (evt.touches.length > 0) {
      startDraw(evt.touches[0]);
    }
  };

  const handleTouchMove = (evt: React.TouchEvent<HTMLCanvasElement>) => {
    // evt.preventDefault();
    if (evt.touches.length > 0) {
      draw(evt.touches[0]);
    }
  };

  const handleTouchEnd = (evt: React.TouchEvent<HTMLCanvasElement>) => {
    evt.preventDefault();
    stopDraw();
  };

  return {
    canvasRef,
    isEmpty,
    clearSignature,
    getSignatureData,
    // Event handlers
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleMouseLeave,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
  };
};
