"use client";
import { useEffect, useState } from "react";

export const useProd = () => {
    const [isProd, setIsProd] = useState(false);

    useEffect(() => {
        if (typeof window !== "undefined") {
            const host = window.location.hostname;
            console.log("hostname", host);
            setIsProd(host === "epic-padel.com" || host === "www.epic-padel.com");
        }
    }, []);

    return { isProd };
}