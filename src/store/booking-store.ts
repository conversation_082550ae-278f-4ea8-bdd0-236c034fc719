import { create } from "zustand";

type SportType = "Padel" | "Pickleball" | "Tennis";

interface BookingState {
    selectedSport: SportType;
    setSelectedSport: (sport: SportType) => void;
    selectedBookingType: string;
    setSelectedBookingType: (type: string) => void;
}

export const useBookingStore = create<BookingState>((set) => ({
    selectedSport: "Padel",
    setSelectedSport: (sport) => set({ selectedSport: sport }),
    selectedBookingType: "",
    setSelectedBookingType: (type) => set({ selectedBookingType: type }),
}));
