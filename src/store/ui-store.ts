import { create } from "zustand";

interface UIState {
  isLoginDialogOpen: boolean;
  isMembershipDialogOpen: boolean;
  isThankYouDialogOpen: boolean;
  selectedPlan: {
    id: number;
    name: string;
    price: number;
    isYearly: boolean;
    min_age?: number;
    max_age?: number;
  } | null;
  openLoginDialog: () => void;
  closeLoginDialog: () => void;
  openMembershipDialog: (plan: {
    id: number;
    name: string;
    price: number;
    isYearly: boolean;
    min_age?: number;
    max_age?: number;
  }) => void;
  closeMembershipDialog: () => void;
  openThankYouDialog: () => void;
  closeThankYouDialog: () => void;
}

export const useUIStore = create<UIState>((set) => ({
  isLoginDialogOpen: false,
  isMembershipDialogOpen: false,
  isThankYouDialogOpen: false,
  selectedPlan: null,
  openLoginDialog: () => set({ isLoginDialogOpen: true }),
  closeLoginDialog: () => set({ isLoginDialogOpen: false }),
  openMembershipDialog: (plan) =>
    set({
      isMembershipDialogOpen: true,
      selectedPlan: plan,
    }),
  closeMembershipDialog: () =>
    set({
      isMembershipDialogOpen: false,
      selectedPlan: null,
    }),
  openThankYouDialog: () => set({ isThankYouDialogOpen: true }),
  closeThankYouDialog: () => set({ isThankYouDialogOpen: false }),
}));
