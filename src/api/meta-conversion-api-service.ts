import axiosInstance from "./axios";

export const sendConversionAPIEvent = async ({ eventName }: { eventName: string }) => {
    try {
        const response = await axiosInstance.post(
            "public/conversion/addEvent",
            { event_name: eventName }
        );
        console.log("sent the conversion api event", eventName, response);
    } catch (error: any) {
        console.log("Sending conversion api event error: ", error);
    }
};